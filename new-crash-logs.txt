--------- beginning of main
06-13 11:20:01.760 12841 13320 E DefaultHttpIssuer: Attempt to consume entity of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> when no request is executing.
06-13 11:20:01.761 12841 13320 E DefaultHttpIssuer: Attempt to close <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> when no request is executing.
06-13 11:20:02.516 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog Request 0xb400007c3c8bea80: 0 https://drivefrontend-pa.googleapis.com/v1/items:get transport: drive::ds::Status::UNAVAILABLE_WHILE_OFFLINE reader: drive::ds::Status::SUCCESS
06-13 11:20:02.516 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog req.hdr 0xb400007c3c8bea80: Content-Type=application/x-protobuf
06-13 11:20:02.516 12841 12963 E <PERSON>o   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog req.hdr 0xb400007c3c8bea80: X-Goog-FieldMask=responses(status,item(last_viewed_by_me_date_millis,folder_color_rgb,starred,open_with_links,resource_key,parent,subscribed,primary_domain_name,has_visitor_permissions,inheritance_broken,quota_bytes_used,shortcut_details(target_id,target_mime_type,target_item,target_resource_key,target_lookup_status),blocking_detectors,default_open_with_link,primary_sync_parent,organization_display_name,customer_id,file_size,modified_date_millis,trashed_date_millis,alternate_link,shared,head_revision_id,last_modifying_user(email_from_account,id,focus_user_id),permission_summary(visibility.type),thumbnail_version,has_legacy_blob_comments,ancestor_has_own_permissions,viewed,content_restrictions.read_only,abuse_notice_reason,spaces,recency_date_reason,approval_summaries,approval_version,owner(organization_display_name,id,focus_user_id,email_from_account),folder_features,readers_can_see_comments,action_item,abuse_is_appeala
06-13 11:20:02.516 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog req.hdr 0xb400007c3c8bea80: x-goog-ext-*********-bin=CgsI/gpCAEgBYgIIAw==
06-13 11:20:02.516 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog Response body 0xb400007c3c8bea80: (no response body)
06-13 11:20:02.516 12841 12963 E Cello   : [12963:NonCelloThread] item_query_handler.cc:1178:OnCloudByIdQuery Failed to get items from the cloud: MessageLite at 0xb400007c3ca98f80
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: [2] failed: com.google.android.apps.docs.common.sync.task.c: Failed to get sync item metadata..
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: com.google.android.apps.docs.common.sync.task.c: Failed to get sync item metadata.
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at com.google.android.apps.docs.common.sync.content.n$a.run(PG:341)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at java.lang.Thread.run(Thread.java:1012)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: Caused by: com.google.android.libraries.drive.core.j: Cello error 2. Network error Cleared request. Failed task: getFiles_203639437(FindByIdsRequest={"id": ["1dr8Co6XGfdjg9VH5I9Zex08TtNW_y_v-MD6_ywieG5g"],"requestDescriptor": {"reason": 1406},"skipLocalstore": true},fetchedCloud=true)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.h.a(PG:19)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.m.d(PG:71)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.item.au.h(PG:87)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.item.ap.a(PG:23)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.impl.cello.jni.SlimJni__Cello_ItemQueryCallback.call(PG:11)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.impl.cello.jni.SlimJni__HttpRequestContext.native_onError(Native Method)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.impl.cello.jni.SlimJni__HttpRequestContext.onError(PG:10)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at androidx.activity.h.run(PG:467)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.common.a.c(PG:3)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at com.google.android.apps.docs.editors.ritz.view.shared.q.call(PG:451)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at com.google.common.util.concurrent.be$b.a(PG:3)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at com.google.common.util.concurrent.ap.run(PG:19)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at com.google.common.util.concurrent.be.run(PG:5)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at java.lang.Thread.run(Thread.java:1012)
06-13 11:20:02.519 12841 13304 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.executor.a.run(PG:6)
06-13 11:20:06.024 12841 13320 E DefaultHttpIssuer: Attempt to consume entity of HttpIssuer when no request is executing.
06-13 11:20:06.024 12841 13320 E DefaultHttpIssuer: Attempt to close HttpIssuer when no request is executing.
06-13 11:20:06.034 12841 13069 E SyncAppWrapper: Sync result - request ID 3, result - FAIL_RETRY, message - Class$obf_mn_0: Error fetching font metadata, http status code:0
06-13 11:20:08.546 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog Request 0xb400007c3c8bfac0: 0 https://drivefrontend-pa.googleapis.com/v1/items:get transport: drive::ds::Status::UNAVAILABLE_WHILE_OFFLINE reader: drive::ds::Status::SUCCESS
06-13 11:20:08.546 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog req.hdr 0xb400007c3c8bfac0: Content-Type=application/x-protobuf
06-13 11:20:08.546 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog req.hdr 0xb400007c3c8bfac0: X-Goog-FieldMask=responses(status,item(title,primary_sync_parent,export_links,blocking_detectors,mime_type,create_date_millis,client_encryption_details(decryption_metadata.aes_256_gcm_chunk_size,decryption_metadata.kacls_id,decryption_metadata.key_format,decryption_metadata.wrapped_key,decryption_metadata.jwt,decryption_metadata.kacls_name,decryption_metadata.guest_idp),team_drive_id,md5_checksum,has_thumbnail,single_parent_id,owned_by_me,recency_date_millis,shared_with_me_date_millis,folder_color_rgb,workspace_id,shortcut_details(target_mime_type,target_id,target_resource_key,target_lookup_status,target_item),spam_metadata(is_inherited_spam,marked_as_spam_date_millis,in_spam_view,is_spam),folder_color,etag,trashed,applied_labels,has_own_permissions,passively_subscribed,detectors,version,action_item,readers_can_see_comments,folder_features,published,approval_version,recency_date_reason,approval_summaries,modified_by_me_
06-13 11:20:08.546 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog req.hdr 0xb400007c3c8bfac0: x-goog-ext-*********-bin=CgsI/gpCAEgBYgIIAw==
06-13 11:20:08.546 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog Response body 0xb400007c3c8bfac0: (no response body)
06-13 11:20:08.546 12841 12963 E Cello   : [12963:NonCelloThread] item_query_handler.cc:1178:OnCloudByIdQuery Failed to get items from the cloud: MessageLite at 0xb400007c3ca9aec0
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: [0] failed: com.google.android.apps.docs.common.sync.task.c: Failed to get sync item metadata..
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: com.google.android.apps.docs.common.sync.task.c: Failed to get sync item metadata.
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at com.google.android.apps.docs.common.sync.content.n$a.run(PG:341)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at java.lang.Thread.run(Thread.java:1012)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: Caused by: com.google.android.libraries.drive.core.j: Cello error 2. Network error Cleared request. Failed task: getFiles_220789567(FindByIdsRequest={"id": ["1_UrvGGgFPvvmnV3i9sdBFFwjDGaQ_1qi0JFdnnywUdk"],"requestDescriptor": {"reason": 1406},"skipLocalstore": true},fetchedCloud=true)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.h.a(PG:19)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.m.d(PG:71)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.item.au.h(PG:87)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.item.ap.a(PG:23)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.impl.cello.jni.SlimJni__Cello_ItemQueryCallback.call(PG:11)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.impl.cello.jni.SlimJni__HttpRequestContext.native_onError(Native Method)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.impl.cello.jni.SlimJni__HttpRequestContext.onError(PG:10)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at androidx.activity.h.run(PG:467)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.common.a.c(PG:3)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at com.google.android.apps.docs.editors.ritz.view.shared.q.call(PG:451)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at com.google.common.util.concurrent.be$b.a(PG:3)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at com.google.common.util.concurrent.ap.run(PG:19)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at com.google.common.util.concurrent.be.run(PG:5)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at java.lang.Thread.run(Thread.java:1012)
06-13 11:20:08.549 12841 13302 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.executor.a.run(PG:6)
06-13 11:20:18.864 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog Request 0xb400007c3c8c0740: 0 https://drivefrontend-pa.googleapis.com/v1/items:get transport: drive::ds::Status::UNAVAILABLE_WHILE_OFFLINE reader: drive::ds::Status::SUCCESS
06-13 11:20:18.864 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog req.hdr 0xb400007c3c8c0740: Content-Type=application/x-protobuf
06-13 11:20:18.865 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog req.hdr 0xb400007c3c8c0740: X-Goog-FieldMask=responses(status,item(file_size,recency_date_millis,customer_id,primary_sync_parent,default_open_with_link,modified_date_millis,blocking_detectors,organization_display_name,primary_domain_name,quota_bytes_used,subscribed,last_viewed_by_me_date_millis,starred,inheritance_broken,open_with_links,resource_key,has_visitor_permissions,owner(id,focus_user_id,email_from_account,organization_display_name),spaces,parent,abuse_notice_reason,viewed,has_legacy_blob_comments,content_restrictions.read_only,last_modifying_user(email_from_account,id,focus_user_id),head_revision_id,ancestor_has_own_permissions,thumbnail_version,permission_summary(visibility.type),trashed_date_millis,alternate_link,shared,explicitly_trashed,id,modified_by_me_date_millis,contains_unsubscribed_children,abuse_is_appealable,action_item,published,folder_features,readers_can_see_comments,approval_summaries,spam_metadata(is_inherited_spam,in_spa
06-13 11:20:18.865 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog req.hdr 0xb400007c3c8c0740: x-goog-ext-*********-bin=CgsI/gpCAEgBYgIIAw==
06-13 11:20:18.865 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog Response body 0xb400007c3c8c0740: (no response body)
06-13 11:20:18.865 12841 12963 E Cello   : [12963:NonCelloThread] item_query_handler.cc:1178:OnCloudByIdQuery Failed to get items from the cloud: MessageLite at 0xb400007c3ca98800
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: [3] failed: com.google.android.apps.docs.common.sync.task.c: Failed to get sync item metadata..
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: com.google.android.apps.docs.common.sync.task.c: Failed to get sync item metadata.
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at com.google.android.apps.docs.common.sync.content.n$a.run(PG:341)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at java.lang.Thread.run(Thread.java:1012)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: Caused by: com.google.android.libraries.drive.core.j: Cello error 2. Network error Cleared request. Failed task: getFiles_196934025(FindByIdsRequest={"id": ["1dxMriywf_NqBds2VFzBB3Zn8bDNB6IAk32fLjcZicnY"],"requestDescriptor": {"reason": 1406},"skipLocalstore": true},fetchedCloud=true)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.h.a(PG:19)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.m.d(PG:71)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.item.au.h(PG:87)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.item.ap.a(PG:23)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.impl.cello.jni.SlimJni__Cello_ItemQueryCallback.call(PG:11)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.impl.cello.jni.SlimJni__HttpRequestContext.native_onError(Native Method)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.impl.cello.jni.SlimJni__HttpRequestContext.onError(PG:10)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at androidx.activity.h.run(PG:467)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.common.a.c(PG:3)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at com.google.android.apps.docs.editors.ritz.view.shared.q.call(PG:451)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at com.google.common.util.concurrent.be$b.a(PG:3)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at com.google.common.util.concurrent.ap.run(PG:19)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at com.google.common.util.concurrent.be.run(PG:5)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at java.lang.Thread.run(Thread.java:1012)
06-13 11:20:18.870 12841 13305 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.executor.a.run(PG:6)
06-13 11:20:23.873  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-13 11:20:23.905  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-13 11:20:23.905  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-13 11:20:24.165  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-13 11:20:24.363  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-13 11:20:24.502  1767  2029 E fpc_tac : fpc_perf_lock_acquire: Failed to acquire perf lock, err: 0
06-13 11:20:24.562  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
--------- beginning of system
06-13 11:20:24.652  2032  2032 E ActivityManager: Cancel pending or running compactions as system is awake
06-13 11:20:24.695  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-13 11:20:24.707  3305  3305 E KeyguardViewMediator: mHideAnimationFinishedRunnable#run: mHideAnimationRun - true
06-13 11:20:24.783  3305  3305 E LockScreenMagazinePreView: getFullScreenLayout()  mRemoteFullScreenView:null
06-13 11:20:24.783  3305  3305 E LockScreenMagazinePreView: getFullScreenLayout()  mRemoteFullScreenView:null
06-13 11:20:24.804  2032  2032 E AudioService.FoldAngelHelper: get fold sensor faild
06-13 11:20:24.804  2032  2032 E AudioService.FoldAngelHelper: get fold sensor faild
06-13 11:20:24.804  2032  2032 E SensorManager: sensor or listener is null
06-13 11:20:24.878  3305  3305 E adTracker: Value ranker_group of type java.lang.String cannot be converted to JSONObject
06-13 11:20:24.905  3305  3655 E perf_hint: Session creation failed, mPreferredRateNanos: -1
06-13 11:20:24.922  3305  3305 E ClockBaseAnimation: com.android.keyguard.clock.animation.classic.ClassicClockAnimation@8b98bc0 doAnimationToAod toAod: false, hasNotification: true
06-13 11:20:24.924 10242 10401 E BtGatt.ContextMap: Context not found for ID 6
06-13 11:20:24.924 10242 10401 E BtGatt.GattService: Advertise app or callback is null
06-13 11:20:24.963  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-13 11:20:24.963  2032  2776 E AppOps  : Bad call made by uid 1002. Package "com.google.android.gms" does not belong to uid 1002.
06-13 11:20:24.964 10242 10493 E BtGatt.ScanManager: Scan already started
06-13 11:20:24.992  2032  2776 E AppOps  : Bad call made by uid 10083. Package "com.miui.weather2" does not belong to uid 1000.
06-13 11:20:25.006  2032  5471 E AppOps  : Bad call made by uid 10083. Package "com.miui.weather2" does not belong to uid 1000.
06-13 11:20:25.024  2032  2776 E AppOps  : Bad call made by uid 10083. Package "com.miui.weather2" does not belong to uid 1000.
06-13 11:20:25.025  2032  2776 E AppOps  : Bad call made by uid 10083. Package "com.miui.weather2" does not belong to uid 1000.
06-13 11:20:25.027  2032  6223 E AppOps  : Bad call made by uid 10083. Package "com.miui.weather2" does not belong to uid 1000.
06-13 11:20:25.069  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-13 11:20:25.122  7106 12412 E libEGL  : pre_cache appList: ,,,
06-13 11:20:25.126  3305  3305 E ReflectUtils: invokeObject
06-13 11:20:25.126  3305  3305 E ReflectUtils: java.lang.reflect.InvocationTargetException
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at java.lang.reflect.Method.invoke(Native Method)
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at com.miui.clock.utils.ReflectUtils.invokeObject(ReflectUtils.java:31)
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at com.miui.clock.utils.DataUtils.isAppInstalledForUser(DataUtils.java:341)
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at com.miui.clock.utils.DataUtils.isHealthAppInstalled(DataUtils.java:326)
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at com.miui.clock.classic.ClassicClockView.filterClassicClockInfo(ClassicClockView.java:178)
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at com.miui.clock.classic.ClassicClockView.updateTime(ClassicClockView.java:316)
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at com.miui.clock.MiuiClockController$1.run(MiuiClockController.java:155)
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at android.os.Handler.handleCallback(Handler.java:942)
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at android.os.Handler.dispatchMessage(Handler.java:99)
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at android.os.Looper.loopOnce(Looper.java:211)
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at android.os.Looper.loop(Looper.java:300)
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at android.app.ActivityThread.main(ActivityThread.java:8503)
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at java.lang.reflect.Method.invoke(Native Method)
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:561)
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:954)
06-13 11:20:25.126  3305  3305 E ReflectUtils: Caused by: android.content.pm.PackageManager$NameNotFoundException: com.mi.health
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at android.app.ApplicationPackageManager.getPackageInfoAsUser(ApplicationPackageManager.java:254)
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	at android.app.ApplicationPackageManager.getPackageInfoAsUser(ApplicationPackageManager.java:242)
06-13 11:20:25.126  3305  3305 E ReflectUtils: 	... 15 more
06-13 11:20:25.146  3305  3305 E AnimationHelper: updateTime mToAod:false mHasNotification: true, mClockType: classic
06-13 11:20:25.147  3305  3305 E ReflectUtils: invokeObject
06-13 11:20:25.147  3305  3305 E ReflectUtils: java.lang.reflect.InvocationTargetException
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at java.lang.reflect.Method.invoke(Native Method)
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at com.miui.clock.utils.ReflectUtils.invokeObject(ReflectUtils.java:31)
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at com.miui.clock.utils.DataUtils.isAppInstalledForUser(DataUtils.java:341)
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at com.miui.clock.utils.DataUtils.isHealthAppInstalled(DataUtils.java:326)
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at com.miui.clock.classic.ClassicClockView.filterClassicClockInfo(ClassicClockView.java:178)
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at com.miui.clock.classic.ClassicClockView.updateTime(ClassicClockView.java:316)
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at com.miui.clock.MiuiClockController$1.run(MiuiClockController.java:155)
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at android.os.Handler.handleCallback(Handler.java:942)
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at android.os.Handler.dispatchMessage(Handler.java:99)
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at android.os.Looper.loopOnce(Looper.java:211)
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at android.os.Looper.loop(Looper.java:300)
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at android.app.ActivityThread.main(ActivityThread.java:8503)
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at java.lang.reflect.Method.invoke(Native Method)
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:561)
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:954)
06-13 11:20:25.147  3305  3305 E ReflectUtils: Caused by: android.content.pm.PackageManager$NameNotFoundException: com.mi.health
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at android.app.ApplicationPackageManager.getPackageInfoAsUser(ApplicationPackageManager.java:254)
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	at android.app.ApplicationPackageManager.getPackageInfoAsUser(ApplicationPackageManager.java:242)
06-13 11:20:25.147  3305  3305 E ReflectUtils: 	... 15 more
06-13 11:20:25.149  3305  3305 E AnimationHelper: updateTime mToAod:false mHasNotification: true, mClockType: classic
06-13 11:20:25.165  3305  3305 E ReflectUtils: invokeObject
06-13 11:20:25.165  3305  3305 E ReflectUtils: java.lang.reflect.InvocationTargetException
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at java.lang.reflect.Method.invoke(Native Method)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at com.miui.clock.utils.ReflectUtils.invokeObject(ReflectUtils.java:31)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at com.miui.clock.utils.DataUtils.isAppInstalledForUser(DataUtils.java:341)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at com.miui.clock.utils.DataUtils.isHealthAppInstalled(DataUtils.java:326)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at com.miui.clock.classic.ClassicClockView.filterClassicClockInfo(ClassicClockView.java:178)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at com.miui.clock.classic.ClassicClockView.updateColor(ClassicClockView.java:156)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at com.android.keyguard.clock.animation.classic.ClassicClockAnimation$3.onUpdate(ClassicClockAnimation.java:155)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at miuix.animation.listener.ListenerNotifier$UpdateNotifier.doNotify(ListenerNotifier.java:67)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at miuix.animation.listener.ListenerNotifier.notifyListenerSet(ListenerNotifier.java:198)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at miuix.animation.listener.ListenerNotifier.notify(ListenerNotifier.java:189)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at miuix.animation.listener.ListenerNotifier.notifyUpdate(ListenerNotifier.java:171)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at miuix.animation.internal.TargetHandler.setValueAndNotify(TargetHandler.java:190)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at miuix.animation.internal.TargetHandler.update(TargetHandler.java:173)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at miuix.animation.internal.TargetHandler.onEnd(TargetHandler.java:203)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at miuix.animation.internal.TargetHandler.handleMessage(TargetHandler.java:67)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at android.os.Handler.dispatchMessage(Handler.java:106)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at android.os.Looper.loopOnce(Looper.java:211)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at android.os.Looper.loop(Looper.java:300)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at android.app.ActivityThread.main(ActivityThread.java:8503)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at java.lang.reflect.Method.invoke(Native Method)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:561)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:954)
06-13 11:20:25.165  3305  3305 E ReflectUtils: Caused by: android.content.pm.PackageManager$NameNotFoundException: com.mi.health
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at android.app.ApplicationPackageManager.getPackageInfoAsUser(ApplicationPackageManager.java:254)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	at android.app.ApplicationPackageManager.getPackageInfoAsUser(ApplicationPackageManager.java:242)
06-13 11:20:25.165  3305  3305 E ReflectUtils: 	... 22 more
06-13 11:20:25.168  3305  3305 E ReflectUtils: invokeObject
06-13 11:20:25.168  3305  3305 E ReflectUtils: java.lang.reflect.InvocationTargetException
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at java.lang.reflect.Method.invoke(Native Method)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at com.miui.clock.utils.ReflectUtils.invokeObject(ReflectUtils.java:31)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at com.miui.clock.utils.DataUtils.isAppInstalledForUser(DataUtils.java:341)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at com.miui.clock.utils.DataUtils.isHealthAppInstalled(DataUtils.java:326)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at com.miui.clock.classic.ClassicClockView.filterClassicClockInfo(ClassicClockView.java:178)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at com.miui.clock.classic.ClassicClockView.updateTime(ClassicClockView.java:316)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at com.miui.clock.classic.ClassicClockView.updateColor(ClassicClockView.java:169)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at com.android.keyguard.clock.animation.classic.ClassicClockAnimation$3.onUpdate(ClassicClockAnimation.java:155)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at miuix.animation.listener.ListenerNotifier$UpdateNotifier.doNotify(ListenerNotifier.java:67)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at miuix.animation.listener.ListenerNotifier.notifyListenerSet(ListenerNotifier.java:198)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at miuix.animation.listener.ListenerNotifier.notify(ListenerNotifier.java:189)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at miuix.animation.listener.ListenerNotifier.notifyUpdate(ListenerNotifier.java:171)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at miuix.animation.internal.TargetHandler.setValueAndNotify(TargetHandler.java:190)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at miuix.animation.internal.TargetHandler.update(TargetHandler.java:173)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at miuix.animation.internal.TargetHandler.onEnd(TargetHandler.java:203)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at miuix.animation.internal.TargetHandler.handleMessage(TargetHandler.java:67)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at android.os.Handler.dispatchMessage(Handler.java:106)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at android.os.Looper.loopOnce(Looper.java:211)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at android.os.Looper.loop(Looper.java:300)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at android.app.ActivityThread.main(ActivityThread.java:8503)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at java.lang.reflect.Method.invoke(Native Method)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:561)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:954)
06-13 11:20:25.168  3305  3305 E ReflectUtils: Caused by: android.content.pm.PackageManager$NameNotFoundException: com.mi.health
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at android.app.ApplicationPackageManager.getPackageInfoAsUser(ApplicationPackageManager.java:254)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	at android.app.ApplicationPackageManager.getPackageInfoAsUser(ApplicationPackageManager.java:242)
06-13 11:20:25.168  3305  3305 E ReflectUtils: 	... 23 more
06-13 11:20:25.205 21937 21937 E NfcService: screen state : android.intent.action.USER_PRESENT
06-13 11:20:25.216  7106 12412 E libboost: fail to open file: Permission denied
06-13 11:20:25.216  7106 12412 E libboost: fail to open node: No such file or directory
06-13 11:20:25.264  7106 12412 E ackageinstaller: DF open fail: Permission denied
06-13 11:20:25.266  7106 12412 E perf_hint: Session creation failed, mPreferredRateNanos: -1
06-13 11:20:25.330 21212 21212 E Launcher.Boost: boost failed
06-13 11:20:25.330 21212 21212 E Launcher.Boost: java.lang.NoSuchMethodError: No static method setThreadPriority(IILjava/lang/String;)V in class Landroid/os/MiuiProcess; or its super classes (declaration of 'android.os.MiuiProcess' appears in /system_ext/framework/miui-framework.jar)
06-13 11:20:25.330 21212 21212 E Launcher.Boost: 	at com.miui.launcher.utils.BoostHelper.boost(BoostHelper.java:81)
06-13 11:20:25.330 21212 21212 E Launcher.Boost: 	at com.miui.home.launcher.Launcher.showPresent(Launcher.java:4124)
06-13 11:20:25.330 21212 21212 E Launcher.Boost: 	at com.miui.home.launcher.common.UnlockAnimationStateMachine.showAnimation(UnlockAnimationStateMachine.java:157)
06-13 11:20:25.330 21212 21212 E Launcher.Boost: 	at com.miui.home.launcher.common.UnlockAnimationStateMachine.onUserPresent(UnlockAnimationStateMachine.java:197)
06-13 11:20:25.330 21212 21212 E Launcher.Boost: 	at com.miui.home.launcher.Launcher$44$1.run(Launcher.java:4154)
06-13 11:20:25.330 21212 21212 E Launcher.Boost: 	at android.os.Handler.handleCallback(Handler.java:942)
06-13 11:20:25.330 21212 21212 E Launcher.Boost: 	at android.os.Handler.dispatchMessage(Handler.java:99)
06-13 11:20:25.330 21212 21212 E Launcher.Boost: 	at android.os.Looper.loopOnce(Looper.java:211)
06-13 11:20:25.330 21212 21212 E Launcher.Boost: 	at android.os.Looper.loop(Looper.java:300)
06-13 11:20:25.330 21212 21212 E Launcher.Boost: 	at android.app.ActivityThread.main(ActivityThread.java:8503)
06-13 11:20:25.330 21212 21212 E Launcher.Boost: 	at java.lang.reflect.Method.invoke(Native Method)
06-13 11:20:25.330 21212 21212 E Launcher.Boost: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:561)
06-13 11:20:25.330 21212 21212 E Launcher.Boost: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:954)
06-13 11:20:25.368 21212 21212 E BranchAllAppsIndicator: changedByBranch: callBack = null
06-13 11:20:25.371  1083 21982 E NxpTml  : _i2c_write() errno : 5
06-13 11:20:25.371  1083 21982 E NxpTml  : PN54X - Error in I2C Write.....
06-13 11:20:25.371  1083 21984 E NxpHal  : write error status = 0x1ff
06-13 11:20:25.371  1083  1083 E NxpHal  : write_unlocked failed - PN54X Maybe in Standby Mode - Retry
06-13 11:20:25.401 21937 21979 E libnfc_nci: [ERROR:nfa_dm_discover.cc(1162)] nfa_dm_cb.disc_cb.entry[0].selected_disc_mask = 0x140000
06-13 11:20:25.401 21937 21979 E libnfc_nci: [ERROR:nfa_dm_discover.cc(1162)] nfa_dm_cb.disc_cb.entry[1].selected_disc_mask = 0x340000
06-13 11:20:25.401 21937 21979 E libnfc_nci: [ERROR:nfa_dm_discover.cc(1162)] nfa_dm_cb.disc_cb.entry[2].selected_disc_mask = 0xe97f
06-13 11:20:25.429 21937 21937 E NfcService: screen state : android.intent.action.USER_PRESENT
06-13 11:20:25.444 21937 21979 E libnfc_nci: [ERROR:nfa_dm_discover.cc(1162)] nfa_dm_cb.disc_cb.entry[0].selected_disc_mask = 0x140000
06-13 11:20:25.444 21937 21979 E libnfc_nci: [ERROR:nfa_dm_discover.cc(1162)] nfa_dm_cb.disc_cb.entry[1].selected_disc_mask = 0x340000
06-13 11:20:25.444 21937 21979 E libnfc_nci: [ERROR:nfa_dm_discover.cc(1162)] nfa_dm_cb.disc_cb.entry[2].selected_disc_mask = 0xe97f
06-13 11:20:25.459 21440   669 E com.miui.appmanager.AppManageUtils: startScan error
06-13 11:20:25.459 21440   669 E com.miui.appmanager.AppManageUtils: java.lang.NoSuchMethodException: android.net.wifi.IWifiManager$Stub$Proxy.startScan [class java.lang.String]
06-13 11:20:25.459 21440   669 E com.miui.appmanager.AppManageUtils: 	at java.lang.Class.getMethod(Class.java:2950)
06-13 11:20:25.459 21440   669 E com.miui.appmanager.AppManageUtils: 	at java.lang.Class.getDeclaredMethod(Class.java:2929)
06-13 11:20:25.459 21440   669 E com.miui.appmanager.AppManageUtils: 	at nf.f.d(Unknown Source:4)
06-13 11:20:25.459 21440   669 E com.miui.appmanager.AppManageUtils: 	at com.miui.appmanager.AppManageUtils.D0(Unknown Source:88)
06-13 11:20:25.459 21440   669 E com.miui.appmanager.AppManageUtils: 	at e3.b$a.a(Unknown Source:29)
06-13 11:20:25.459 21440   669 E com.miui.appmanager.AppManageUtils: 	at e3.b$a.doInBackground(Unknown Source:2)
06-13 11:20:25.459 21440   669 E com.miui.appmanager.AppManageUtils: 	at android.os.AsyncTask$3.call(AsyncTask.java:394)
06-13 11:20:25.459 21440   669 E com.miui.appmanager.AppManageUtils: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
06-13 11:20:25.459 21440   669 E com.miui.appmanager.AppManageUtils: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
06-13 11:20:25.459 21440   669 E com.miui.appmanager.AppManageUtils: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
06-13 11:20:25.459 21440   669 E com.miui.appmanager.AppManageUtils: 	at java.lang.Thread.run(Thread.java:1012)
06-13 11:20:25.726  1707 19018 E OMX-VDEC-1080P: Unsupported output color format for c2d (2141391876)
06-13 11:20:25.726  1707 19018 E OMX-VDEC-1080P: Setting color format failed
06-13 11:20:25.770  2032  2666 E MiuiActivityController: MiuiLog-ActivityObserver: There was something wrong : null
06-13 11:20:25.799  1109  4499 E ANDR-PERF-LM: VmRssMeter:: start() 447: Could not find pid, can not collect vmrss data
06-13 11:20:25.802  1109  4500 E ANDR-PERF-LM: VmRssMeter:: start() 447: Could not find pid, can not collect vmrss data
06-13 11:20:25.802  1109  4500 E ANDR-PERF-LM: AdaptiveLaunch: writeToDataBase() 64: Meter aborted or could not get meter data for this run
06-13 11:20:25.811  1707 25544 E OMX-VDEC-1080P: Unsupported output color format for c2d (2141391876)
06-13 11:20:25.811  1707 25544 E OMX-VDEC-1080P: Setting color format failed
06-13 11:20:25.815  1707 25544 E OMX-VDEC-1080P: Extension: OMX.google.android.index.storeANWBufferInMetadata not implemented
06-13 11:20:25.815  1707 25544 E OMX-VDEC-1080P: Extension: OMX.google.android.index.configureVideoTunnelMode not implemented
06-13 11:20:25.815  1707 25544 E OMX-VDEC-1080P: Extension: OMX.google.android.index.useAndroidNativeBuffer is supported
06-13 11:20:25.815  1707 25544 E OMX-VDEC-1080P: get_parameter: unknown param 7f000046
06-13 11:20:25.815  1707 25544 E OMXNodeInstance: getParameter(0xec2aca44:qcom.decoder.hevc, ??(0x7f000046)) ERROR: UnsupportedIndex(0x8000101a)
06-13 11:20:25.816  1707 25544 E OMX-VDEC-1080P: Setparameter: unknown param 2130706502
06-13 11:20:25.816  1707 25544 E OMX-VDEC-1080P: set_parameter: Error: 0x8000101a, setting param 0x7f000046
06-13 11:20:25.816  1707 25544 E OMXNodeInstance: setParameter(0xec2aca44:qcom.decoder.hevc, ??(0x7f000046)) ERROR: UnsupportedIndex(0x8000101a)
06-13 11:20:25.816  1707 25544 E OMX-VDEC-1080P: Extension: OMX.google.android.index.storeANWBufferInMetadata not implemented
06-13 11:20:25.816  1707 25544 E OMX-VDEC-1080P: Extension: OMX.google.android.index.configureVideoTunnelMode not implemented
06-13 11:20:25.816  1707 25544 E OMX-VDEC-1080P: Extension: OMX.google.android.index.useAndroidNativeBuffer is supported
06-13 11:20:25.816  1707 25544 E OMX-VDEC-1080P: get_parameter: unknown param 7f000046
06-13 11:20:25.817  1707 25544 E OMXNodeInstance: getParameter(0xec07a104:qcom.decoder.hevc, ??(0x7f000046)) ERROR: UnsupportedIndex(0x8000101a)
06-13 11:20:25.817  1707 19018 E OMX-VDEC-1080P: Setparameter: unknown param 2130706502
06-13 11:20:25.817  1707 19018 E OMX-VDEC-1080P: set_parameter: Error: 0x8000101a, setting param 0x7f000046
06-13 11:20:25.817  1707 19018 E OMXNodeInstance: setParameter(0xec07a104:qcom.decoder.hevc, ??(0x7f000046)) ERROR: UnsupportedIndex(0x8000101a)
06-13 11:20:25.817  1743  1938 E Codec2-ComponentInterface: We have a failed config
06-13 11:20:25.817  1743 18809 E Codec2-ComponentInterface: We have a failed config
06-13 11:20:25.819  1707  6406 E OMX-VDEC-1080P: set_parameter: Error: 0x80001019, setting param 0x7f00005d
06-13 11:20:25.819  1707  6406 E OMXNodeInstance: setParameter(0xec07a104:qcom.decoder.hevc, OMX.google.android.index.allocateNativeHandle(0x7f00005d): Output:1 en=0) ERROR: UnsupportedSetting(0x80001019)
06-13 11:20:25.819  1707  6406 E OMX-VDEC-1080P: Extension: OMX.google.android.index.storeANWBufferInMetadata not implemented
06-13 11:20:25.819  1707 22436 E OMX-VDEC-1080P: set_parameter: Error: 0x80001019, setting param 0x7f00005d
06-13 11:20:25.819  1707 22436 E OMXNodeInstance: setParameter(0xec2aca44:qcom.decoder.hevc, OMX.google.android.index.allocateNativeHandle(0x7f00005d): Output:1 en=0) ERROR: UnsupportedSetting(0x80001019)
06-13 11:20:25.819  1707 22436 E OMX-VDEC-1080P: Extension: OMX.google.android.index.storeANWBufferInMetadata not implemented
06-13 11:20:25.819  1707 14336 E OMX-VDEC-1080P: get_parameter: unknown param 6f600011
06-13 11:20:25.819  1707 14336 E OMXNodeInstance: getParameter(0xec07a104:qcom.decoder.hevc, ??(0x6f600011)) ERROR: UnsupportedIndex(0x8000101a)
06-13 11:20:25.819  1707 22436 E OMX-VDEC-1080P: get_parameter: unknown param 6f600011
06-13 11:20:25.819  1707 22436 E OMXNodeInstance: getParameter(0xec2aca44:qcom.decoder.hevc, ??(0x6f600011)) ERROR: UnsupportedIndex(0x8000101a)
06-13 11:20:25.820  1707  7049 E OMX-VDEC-1080P: Setparameter: unknown param 2130706434
06-13 11:20:25.820  1707  2569 E OMX-VDEC-1080P: Setparameter: unknown param 2130706434
06-13 11:20:25.820  1707  7049 E OMX-VDEC-1080P: set_parameter: Error: 0x8000101a, setting param 0x7f000002
06-13 11:20:25.820  1707  2569 E OMX-VDEC-1080P: set_parameter: Error: 0x8000101a, setting param 0x7f000002
06-13 11:20:25.820  1707  7049 E OMXNodeInstance: setParameter(0xec07a104:qcom.decoder.hevc, ??(0x7f000002)) ERROR: UnsupportedIndex(0x8000101a)
06-13 11:20:25.820  1707  2569 E OMXNodeInstance: setParameter(0xec2aca44:qcom.decoder.hevc, ??(0x7f000002)) ERROR: UnsupportedIndex(0x8000101a)
06-13 11:20:25.825  1707 22183 E OMXNodeInstance: getConfig(0xec2aca44:qcom.decoder.hevc, ??(0x7f000062)) ERROR: UnsupportedSetting(0x80001019)
06-13 11:20:25.826  1707 22183 E OMXNodeInstance: getConfig(0xec07a104:qcom.decoder.hevc, ??(0x7f000062)) ERROR: UnsupportedSetting(0x80001019)
06-13 11:20:25.840  1743  1938 E Codec2-ComponentInterface: We have a failed config
06-13 11:20:25.840  1743 18809 E Codec2-ComponentInterface: We have a failed config
06-13 11:20:25.940  1707 25544 E OMX-VDEC-1080P: Extension: OMX.google.android.index.AndroidNativeBufferConsumerUsage not implemented
06-13 11:20:25.970  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-13 11:20:25.976  1707 14336 E OMX-VDEC-1080P: Extension: OMX.google.android.index.AndroidNativeBufferConsumerUsage not implemented
06-13 11:20:26.020  1707 14336 E OMXNodeInstance: getConfig(0xec07a104:qcom.decoder.hevc, ??(0x7f000062)) ERROR: UnsupportedSetting(0x80001019)
06-13 11:20:26.025  1707 22436 E OMX-VDEC-1080P: Extension: OMX.google.android.index.AndroidNativeBufferConsumerUsage not implemented
06-13 11:20:26.181 21212 21212 E RotationHelper: setCurrentTransitionRequest: request=2
06-13 11:20:26.186 21212 21212 E RotationHelper: notifyChange activityFlags=1, mMultiWindowRotationRequest=3, mCurrentTransitionRequest=2, mCurrentStateRequest=0
06-13 11:20:26.218 21212 21212 E BlurUtils: fastBlur ratio=0.0   withAnim=false   sBlurRatioValue=0.0
06-13 11:20:26.219 21212 21212 E Launcher: changeViewByFsGestureState,  view=ShortcutMenuLayer,  alpha=0.0,  scale=0.85
06-13 11:20:26.239  1707  6786 E OMXNodeInstance: getConfig(0xec07a104:qcom.decoder.hevc, ??(0x7f000062)) ERROR: UnsupportedSetting(0x80001019)
06-13 11:20:26.269 21212 21227 E ClipAnimationHelper: updateSourceStack  mSourceInsets=Rect(0, 80 - 0, 44), mSourceStackBounds=Rect(0, 0 - 1080, 2400)
06-13 11:20:26.269 21212 21227 E ClipAnimationHelper: updateHomeStack  mSourceInsets=Rect(0, 80 - 0, 44), mHomeStackBounds=Rect(0, 0 - 1080, 2400)
06-13 11:20:26.270 21212 21227 E ClipAnimationHelper: updateTargetRect  mSourceRect=RectF(0.0, 80.0, 1080.0, 2430.7234)   mTargetRect=RectF(305.0, 80.0, 775.0, 1103.0)   mSourceWindowClipInsets=RectF(0.0, 80.0, 0.0, 0.0)   mHomeStackBounds=Rect(0, 0 - 1080, 2400)   targetRect=Rect(305, 80 - 775, 1103)
06-13 11:20:26.274  1707 22436 E OMXNodeInstance: getConfig(0xec2aca44:qcom.decoder.hevc, ??(0x7f000062)) ERROR: UnsupportedSetting(0x80001019)
06-13 11:20:26.284 21212 21315 E StateBroadcastUtils: sendFsStateBroadCast state=taskSnapshot   channel=gesture   packageName=
06-13 11:20:26.322  2032  4126 E SurfaceControl: result = 0, buffer == nullptr = 0
06-13 11:20:26.322  2032  4126 E SurfaceControl: screenshotHardwareBuffer is null : 0
06-13 11:20:26.326  2032  2908 E ProcessManager: error write exception log
06-13 11:20:26.328  1707 22436 E OMX-VDEC-1080P: Extension: OMX.google.android.index.AndroidNativeBufferConsumerUsage not implemented
06-13 11:20:26.349  2032  2908 E ProcessManager: error write exception log
06-13 11:20:26.350  1707  6406 E OMXNodeInstance: getConfig(0xec2aca44:qcom.decoder.hevc, ??(0x7f000062)) ERROR: UnsupportedSetting(0x80001019)
06-13 11:20:26.386 21212 21315 E StateBroadcastUtils: sendFsStateBroadCast state=crossSafeArea   channel=gesture   packageName=
06-13 11:20:26.408  1743 21307 E Codec2-ComponentInterface: We have a failed config
06-13 11:20:26.411  2032  2032 E Fingerprint21: onAcquired for non-acquisition client: null
06-13 11:20:26.420  1743  1938 E Codec2-ComponentInterface: We have a failed config
06-13 11:20:26.451  1123  1123 E DisplayFeatureHal: setFeatureEnable: failed to set fps(1)
06-13 11:20:26.475  2032  2908 E ProcessManager: error write exception log
06-13 11:20:26.598  1053 20956 E volume_listener: check_and_set_gain_dep_cal: Failed to set gain dep cal level
06-13 11:20:26.613  2032  2908 E ProcessManager: error write exception log
06-13 11:20:26.652 21212 21212 E Launcher: changeViewByFsGestureState,  view=ShortcutMenuLayer,  alpha=0.0,  scale=0.85
06-13 11:20:26.653 21212 21212 E OverviewState: onStateEnabled
06-13 11:20:26.653 21212 21212 E ActivityManagerWrapper: get all recent tasks force including 8519
06-13 11:20:26.655 21212 21315 E Launcher: notifyBackGestureStatus:run usingFsGesture=true   show=true   focus=false   pause=true
06-13 11:20:26.657  1707 22436 E android.hardware.media.omx@1.0-service: Service not available yet
06-13 11:20:26.663  1707 22436 E OMX-VDEC-1080P: Unsupported output color format for c2d (2141391876)
06-13 11:20:26.663  1707 22436 E OMX-VDEC-1080P: Setting color format failed
06-13 11:20:26.674  1707 22436 E OMX-VDEC-1080P: Extension: OMX.google.android.index.storeANWBufferInMetadata not implemented
06-13 11:20:26.674  1707 22436 E OMX-VDEC-1080P: Extension: OMX.google.android.index.configureVideoTunnelMode not implemented
06-13 11:20:26.674  1707 22436 E OMX-VDEC-1080P: Extension: OMX.google.android.index.useAndroidNativeBuffer is supported
06-13 11:20:26.674  1707 22436 E OMX-VDEC-1080P: get_parameter: unknown param 7f000046
06-13 11:20:26.674  1707 22436 E OMXNodeInstance: getParameter(0xebec3804:qcom.decoder.avc, ??(0x7f000046)) ERROR: UnsupportedIndex(0x8000101a)
06-13 11:20:26.674  1707  6406 E OMX-VDEC-1080P: Setparameter: unknown param 2130706502
06-13 11:20:26.674  1707  6406 E OMX-VDEC-1080P: set_parameter: Error: 0x8000101a, setting param 0x7f000046
06-13 11:20:26.674  1707  6406 E OMXNodeInstance: setParameter(0xebec3804:qcom.decoder.avc, ??(0x7f000046)) ERROR: UnsupportedIndex(0x8000101a)
06-13 11:20:26.681  1707 22436 E OMX-VDEC-1080P: set_parameter: Error: 0x80001019, setting param 0x7f00005d
06-13 11:20:26.681  1707 22436 E OMXNodeInstance: setParameter(0xebec3804:qcom.decoder.avc, OMX.google.android.index.allocateNativeHandle(0x7f00005d): Output:1 en=0) ERROR: UnsupportedSetting(0x80001019)
06-13 11:20:26.681  1707 22436 E OMX-VDEC-1080P: Extension: OMX.google.android.index.storeANWBufferInMetadata not implemented
06-13 11:20:26.681  1707  6406 E OMX-VDEC-1080P: get_parameter: unknown param 6f600011
06-13 11:20:26.681  1707  6406 E OMXNodeInstance: getParameter(0xebec3804:qcom.decoder.avc, ??(0x6f600011)) ERROR: UnsupportedIndex(0x8000101a)
06-13 11:20:26.682  1707 23933 E OMX-VDEC-1080P: Setparameter: unknown param 2130706434
06-13 11:20:26.682  1707 23933 E OMX-VDEC-1080P: set_parameter: Error: 0x8000101a, setting param 0x7f000002
06-13 11:20:26.682  1707 23933 E OMXNodeInstance: setParameter(0xebec3804:qcom.decoder.avc, ??(0x7f000002)) ERROR: UnsupportedIndex(0x8000101a)
06-13 11:20:26.685  1707  6958 E OMXNodeInstance: getConfig(0xebec3804:qcom.decoder.avc, ??(0x7f000062)) ERROR: UnsupportedSetting(0x80001019)
06-13 11:20:26.690 21937 21937 E NfcService: screen state : android.intent.action.SCREEN_ON
06-13 11:20:26.692  1053 18851 E DlbEffectContext: setParam() received unknown parameter 20
06-13 11:20:26.693  1248  1771 E AudioPolicyService: AudioCommandThread() sending outputdevice changed(id=2)  to 2
06-13 11:20:26.694  1083 21982 E NxpTml  : _i2c_write() errno : 5
06-13 11:20:26.694  1083 21982 E NxpTml  : PN54X - Error in I2C Write.....
06-13 11:20:26.699  1083 21984 E NxpHal  : write error status = 0x1ff
06-13 11:20:26.699  1083  1083 E NxpHal  : write_unlocked failed - PN54X Maybe in Standby Mode - Retry
06-13 11:20:26.716 21212 21212 E ActivityManagerWrapper: getRecentTasks: size=4
06-13 11:20:26.716 21937 21979 E libnfc_nci: [ERROR:nfa_dm_discover.cc(1162)] nfa_dm_cb.disc_cb.entry[0].selected_disc_mask = 0x140000
06-13 11:20:26.716 21212 21212 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8519   userId=0   baseIntent=Intent { act=null flag=411041792 cmp=ComponentInfo{com.miui.global.packageinstaller/com.miui.global.packageinstaller.activity.ScanActivity} }
06-13 11:20:26.716 21937 21979 E libnfc_nci: [ERROR:nfa_dm_discover.cc(1162)] nfa_dm_cb.disc_cb.entry[1].selected_disc_mask = 0x340000
06-13 11:20:26.716 21212 21212 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8518   userId=0   baseIntent=Intent { act=android.intent.action.VIEW flag=335544320 cmp=ComponentInfo{com.android.vending/com.google.android.finsky.activities.MainActivity} }
06-13 11:20:26.716 21212 21212 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8517   userId=0   baseIntent=Intent { act=android.intent.action.VIEW flag=335552512 cmp=ComponentInfo{com.android.chrome/org.chromium.chrome.browser.ChromeTabbedActivity} }
06-13 11:20:26.716 21937 21979 E libnfc_nci: [ERROR:nfa_dm_discover.cc(1162)] nfa_dm_cb.disc_cb.entry[2].selected_disc_mask = 0xe97f
06-13 11:20:26.716 21212 21212 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8515   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.whatsapp/com.whatsapp.Main} }
06-13 11:20:26.763  2032  2908 E ProcessManager: error write exception log
06-13 11:20:26.770  1248 28329 E AudioFlingerImpl: open /proc/1697/cmdline error
06-13 11:20:26.777  1707  6406 E OMX-VDEC-1080P: Extension: OMX.google.android.index.AndroidNativeBufferConsumerUsage not implemented
06-13 11:20:26.781  1707  6406 E OMX-VDEC-1080P: Extension: OMX.QTI.index.config.video.getdsmode not implemented
06-13 11:20:26.809  1053 20956 E volume_listener: check_and_set_gain_dep_cal: Failed to set gain dep cal level
06-13 11:20:26.810  1053 18851 E volume_listener: check_and_set_gain_dep_cal: Failed to set gain dep cal level
06-13 11:20:26.814  1707  6406 E OMXNodeInstance: getConfig(0xebec3804:qcom.decoder.avc, ??(0x7f000062)) ERROR: UnsupportedSetting(0x80001019)
06-13 11:20:26.839 21212 21212 E RotationHelper: setCurrentStateRequest: request=1
06-13 11:20:26.840 21212 21212 E RotationHelper: notifyChange activityFlags=1, mMultiWindowRotationRequest=3, mCurrentTransitionRequest=2, mCurrentStateRequest=1
06-13 11:20:26.846 21212 21212 E StateManager: setWorkspaceProperty  state=OverviewState
06-13 11:20:26.846 21212 21212 E RecentsViewStateController: setState: state=OverviewState
06-13 11:20:26.846 21212 21212 E OverviewState: onStateTransitionEnd
06-13 11:20:26.846 21212 21212 E RotationHelper: setCurrentStateRequest: request=1
06-13 11:20:26.847  1053 20639 E msm8974_platform: platform_check_backends_match: Invalid snd_device = 
06-13 11:20:26.848 21212 21212 E ActivityManagerWrapper: get all recent tasks force including 8519
06-13 11:20:26.851 21212 21212 E ActivityManagerWrapper: getRecentTasks: size=4
06-13 11:20:26.851 21212 21212 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8519   userId=0   baseIntent=Intent { act=null flag=411041792 cmp=ComponentInfo{com.miui.global.packageinstaller/com.miui.global.packageinstaller.activity.ScanActivity} }
06-13 11:20:26.851 21212 21212 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8518   userId=0   baseIntent=Intent { act=android.intent.action.VIEW flag=335544320 cmp=ComponentInfo{com.android.vending/com.google.android.finsky.activities.MainActivity} }
06-13 11:20:26.851 21212 21212 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8517   userId=0   baseIntent=Intent { act=android.intent.action.VIEW flag=335552512 cmp=ComponentInfo{com.android.chrome/org.chromium.chrome.browser.ChromeTabbedActivity} }
06-13 11:20:26.851 21212 21212 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8515   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.whatsapp/com.whatsapp.Main} }
06-13 11:20:26.860  1053 20639 E soundtrigger: audio_extn_sound_trigger_update_stream_status: invalid input device 0x0, for event 3
06-13 11:20:26.883 21212 21212 E RotationHelper: setCurrentTransitionRequest: request=0
06-13 11:20:26.883 21212 21212 E RotationHelper: notifyChange activityFlags=1, mMultiWindowRotationRequest=3, mCurrentTransitionRequest=0, mCurrentStateRequest=1
06-13 11:20:26.886 21212 21212 E BlurUtils: fastBlur ratio=1.0   withAnim=true   sBlurRatioValue=0.0
06-13 11:20:26.894 21212 21315 E StateBroadcastUtils: sendFsStateBroadCast state=toRecents   channel=gesture   packageName=
06-13 11:20:26.916 21212 21212 E BlurUtils: fastBlurDirectly ratio=0.01007247   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:26.949 21212 21212 E BlurUtils: fastBlurDirectly ratio=0.09183034   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:26.963  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-13 11:20:26.973  2032  2908 E ProcessManager: error write exception log
06-13 11:20:26.982 21212 21212 E BlurUtils: fastBlurDirectly ratio=0.24278033   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:27.000  1053 20639 E ACDB-LOADER: [acdb_loader_adsp_set_audio_cal] active device/stream not found (result=-100) for topology 0x1000a106 and apptype 0x11134
06-13 11:20:27.000  1053 20639 E ACDB-LOADER: [acdb_loader_adsp_set_audio_cal] set parameters failed with status -100
06-13 11:20:27.000  1053 20639 E ACDB-LOADER: [acdb_loader_adsp_set_audio_cal] active device/stream not found (result=-100) for topology 0x1000a106 and apptype 0x11131
06-13 11:20:27.000  1053 20639 E ACDB-LOADER: [acdb_loader_adsp_set_audio_cal] set parameters failed with status -100
06-13 11:20:27.003  1053 20639 E send_data_to_xlog: get_int_by_key: unable to get property persist.vendor.audio.scenario
06-13 11:20:27.003  1053 20639 E send_data_to_xlog: the EarsCompensation is closed
06-13 11:20:27.015 21212 21212 E BlurUtils: fastBlurDirectly ratio=0.43733343   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:27.020  1248  1771 E AudioPolicyService: AudioCommandThread() processing output device changed 2 2
06-13 11:20:27.022  1248 27218 E AudioFlingerImpl: open /proc/1697/cmdline error
06-13 11:20:27.026  1248  5475 E AudioFlingerImpl: open /proc/1697/cmdline error
06-13 11:20:27.027  2032  4126 E AudioSystem: onAudioOutputDeviceChanged (2, 2)
06-13 11:20:27.027  3305 20905 E AudioSystem: onAudioOutputDeviceChanged (2, 2)
06-13 11:20:27.028 21440 31605 E AudioSystem: onAudioOutputDeviceChanged (2, 2)
06-13 11:20:27.028  1248  1771 E AudioSystem: onAudioOutputDeviceChanged (2, 2)
06-13 11:20:27.028 26284 26298 E AudioSystem: onAudioOutputDeviceChanged (2, 2)
06-13 11:20:27.028  3301  3326 E AudioSystem: onAudioOutputDeviceChanged (2, 2)
06-13 11:20:27.029 10242 14401 E AudioSystem: onAudioOutputDeviceChanged (2, 2)
06-13 11:20:27.029 30217  7969 E AudioSystem: onAudioOutputDeviceChanged (2, 2)
06-13 11:20:27.029  1697 18342 E AudioSystem: onAudioOutputDeviceChanged (2, 2)
06-13 11:20:27.033  1707 18306 E OMX-VDEC-1080P: Extension: OMX.QTI.index.config.video.getdsmode not implemented
06-13 11:20:27.048 21212 21212 E BlurUtils: fastBlurDirectly ratio=0.64250964   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:27.055  1248 27218 E AudioFlingerImpl: open /proc/1697/cmdline error
06-13 11:20:27.081  1248 28329 E AudioFlingerImpl: open /proc/1697/cmdline error
06-13 11:20:27.082 21212 21212 E BlurUtils: fastBlurDirectly ratio=0.82352793   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:27.087  1707 18306 E OMX-VDEC-1080P: Extension: OMX.QTI.index.config.video.getdsmode not implemented
06-13 11:20:27.114 21212 21212 E BlurUtils: fastBlurDirectly ratio=0.9497026   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:27.147 21212 21212 E BlurUtils: fastBlurDirectly ratio=0.99964476   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:27.164 21212 21212 E BlurUtils: fastBlurDirectly ratio=1.0   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:27.207  2032  2908 E ProcessManager: error write exception log
06-13 11:20:27.249  2032  2776 E SurfaceControl: result = 0, buffer == nullptr = 0
06-13 11:20:27.250  2032  2776 E SurfaceControl: screenshotHardwareBuffer is null : 0
06-13 11:20:27.362  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-13 11:20:27.397 21212 21212 E BlurUtils: fastBlurDirectly ratio=1.0   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:27.407  1109  1282 E ANDR-PERF-OPTSHANDLER: Perflock resource /sys/class/devfreq/soc:qcom,cpu-llcc-ddr-bw/min_freq not supported
06-13 11:20:27.407  1109  1282 E ANDR-PERF-RESOURCEQS: Failed to apply optimization [12, 4]
06-13 11:20:27.424 21212 21212 E RotationHelper: setCurrentTransitionRequest: request=0
06-13 11:20:27.430 21212 21315 E Launcher: notifyBackGestureStatus:run usingFsGesture=true   show=true   focus=false   pause=false
06-13 11:20:27.497 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:27.499  2032  2666 E MiuiActivityController: MiuiLog-ActivityObserver: There was something wrong : null
06-13 11:20:27.502 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=4
06-13 11:20:27.502 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8519   userId=0   baseIntent=Intent { act=null flag=411041792 cmp=ComponentInfo{com.miui.global.packageinstaller/com.miui.global.packageinstaller.activity.ScanActivity} }
06-13 11:20:27.502 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8518   userId=0   baseIntent=Intent { act=android.intent.action.VIEW flag=335544320 cmp=ComponentInfo{com.android.vending/com.google.android.finsky.activities.MainActivity} }
06-13 11:20:27.502 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8517   userId=0   baseIntent=Intent { act=android.intent.action.VIEW flag=335552512 cmp=ComponentInfo{com.android.chrome/org.chromium.chrome.browser.ChromeTabbedActivity} }
06-13 11:20:27.502 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8515   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.whatsapp/com.whatsapp.Main} }
06-13 11:20:27.508 30112 30246 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:27.511 30112 30246 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:27.578  2032  6167 E AppOps  : Bad call made by uid 10083. Package "com.miui.weather2" does not belong to uid 10077.
06-13 11:20:27.620  1109  1282 E ANDR-PERF-OPTSHANDLER: Perflock resource /sys/class/devfreq/soc:qcom,cpu-llcc-ddr-bw/min_freq not supported
06-13 11:20:27.620  1109  1282 E ANDR-PERF-RESOURCEQS: Failed to apply optimization [12, 4]
06-13 11:20:27.620  2032  6167 E AppOps  : Bad call made by uid 10083. Package "com.miui.weather2" does not belong to uid 10077.
06-13 11:20:27.632 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:27.634  2032  2908 E ProcessManager: error write exception log
06-13 11:20:27.635 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=3
06-13 11:20:27.635 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8518   userId=0   baseIntent=Intent { act=android.intent.action.VIEW flag=335544320 cmp=ComponentInfo{com.android.vending/com.google.android.finsky.activities.MainActivity} }
06-13 11:20:27.635 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8517   userId=0   baseIntent=Intent { act=android.intent.action.VIEW flag=335552512 cmp=ComponentInfo{com.android.chrome/org.chromium.chrome.browser.ChromeTabbedActivity} }
06-13 11:20:27.635 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8515   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.whatsapp/com.whatsapp.Main} }
06-13 11:20:27.664  2032  4126 E AppOps  : Bad call made by uid 10083. Package "com.miui.weather2" does not belong to uid 10077.
06-13 11:20:27.666  2032  3074 E AppOps  : Bad call made by uid 10083. Package "com.miui.weather2" does not belong to uid 10077.
06-13 11:20:27.667  2032  4125 E AppOps  : Bad call made by uid 10083. Package "com.miui.weather2" does not belong to uid 10077.
06-13 11:20:27.684  2032  2780 E ConnectivityService: RemoteException caught trying to send a callback msg for NetworkRequest [ TRACK_DEFAULT id=55246, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10152 RequestorUid: 10152 RequestorPkg: com.miui.global.packageinstaller UnderlyingNetworks: Null] ]
06-13 11:20:27.684  2032  2780 E ConnectivityService: RemoteException caught trying to send a callback msg for NetworkRequest [ LISTEN id=55248, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Forbidden: LOCAL_NETWORK Uid: 10152 RequestorUid: 10152 RequestorPkg: com.miui.global.packageinstaller UnderlyingNetworks: Null] ]
06-13 11:20:27.686  2032  2780 E ConnectivityService: RemoteException caught trying to send a callback msg for NetworkRequest [ TRACK_DEFAULT id=55331, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10152 RequestorUid: 10152 RequestorPkg: com.miui.global.packageinstaller UnderlyingNetworks: Null] ]
06-13 11:20:27.718 30112 30246 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:27.720 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:27.729 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=3
06-13 11:20:27.729 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8518   userId=0   baseIntent=Intent { act=android.intent.action.VIEW flag=335544320 cmp=ComponentInfo{com.android.vending/com.google.android.finsky.activities.MainActivity} }
06-13 11:20:27.729 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8517   userId=0   baseIntent=Intent { act=android.intent.action.VIEW flag=335552512 cmp=ComponentInfo{com.android.chrome/org.chromium.chrome.browser.ChromeTabbedActivity} }
06-13 11:20:27.729 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8515   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.whatsapp/com.whatsapp.Main} }
06-13 11:20:27.732 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:27.734 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=3
06-13 11:20:27.734 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8518   userId=0   baseIntent=Intent { act=android.intent.action.VIEW flag=335544320 cmp=ComponentInfo{com.android.vending/com.google.android.finsky.activities.MainActivity} }
06-13 11:20:27.734 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8517   userId=0   baseIntent=Intent { act=android.intent.action.VIEW flag=335552512 cmp=ComponentInfo{com.android.chrome/org.chromium.chrome.browser.ChromeTabbedActivity} }
06-13 11:20:27.734 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8515   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.whatsapp/com.whatsapp.Main} }
06-13 11:20:27.735 30112 30246 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:27.790 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:27.793 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=2
06-13 11:20:27.793 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8517   userId=0   baseIntent=Intent { act=android.intent.action.VIEW flag=335552512 cmp=ComponentInfo{com.android.chrome/org.chromium.chrome.browser.ChromeTabbedActivity} }
06-13 11:20:27.793 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8515   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.whatsapp/com.whatsapp.Main} }
06-13 11:20:27.806  2032  6068 E TaskPersister: File error accessing recents directory (directory doesn't exist?).
06-13 11:20:27.840  1673  2499 E CameraService: notifyMonitoredUids: Failed to trigger permission callback: -129
06-13 11:20:27.863 13780 13780 E Zygote  : process_name_ptr:13780 com.google.android.apps.messaging
06-13 11:20:27.877  1109  1282 E ANDR-PERF-OPTSHANDLER: Perflock resource /sys/class/devfreq/soc:qcom,cpu-llcc-ddr-bw/min_freq not supported
06-13 11:20:27.877  1109  1282 E ANDR-PERF-RESOURCEQS: Failed to apply optimization [12, 4]
06-13 11:20:27.889  2032  2908 E ProcessManager: error write exception log
06-13 11:20:27.889  1697 13725 E Surface : queueBuffer: error queuing buffer, -32
06-13 11:20:27.889  1697 13725 E ACodec  : queueBuffer failed in onOutputBufferDrained: -32
06-13 11:20:27.889  1697 13725 E ACodec  : signalError(omxError 0x80001001, internalError -2147483648)
06-13 11:20:27.889  1697 13725 E Surface : queueBuffer: error queuing buffer, -32
06-13 11:20:27.889  1697 13725 E ACodec  : queueBuffer failed in onOutputBufferDrained: -32
06-13 11:20:27.889  1697 13725 E ACodec  : signalError(omxError 0x80001001, internalError -2147483648)
06-13 11:20:27.890  1697 13725 E Surface : queueBuffer: error queuing buffer, -32
06-13 11:20:27.890  1697 13725 E ACodec  : queueBuffer failed in onOutputBufferDrained: -32
06-13 11:20:27.890  1697 13725 E ACodec  : signalError(omxError 0x80001001, internalError -2147483648)
06-13 11:20:27.890  1697 13725 E Surface : queueBuffer: error queuing buffer, -32
06-13 11:20:27.890  1697 13725 E ACodec  : queueBuffer failed in onOutputBufferDrained: -32
06-13 11:20:27.890  1697 13725 E ACodec  : signalError(omxError 0x80001001, internalError -2147483648)
06-13 11:20:27.890  1697 13725 E Surface : queueBuffer: error queuing buffer, -32
06-13 11:20:27.890  1697 13725 E ACodec  : queueBuffer failed in onOutputBufferDrained: -32
06-13 11:20:27.890  1697 13725 E ACodec  : signalError(omxError 0x80001001, internalError -2147483648)
06-13 11:20:27.890  1697 13725 E Surface : queueBuffer: error queuing buffer, -32
06-13 11:20:27.890  1697 13725 E ACodec  : queueBuffer failed in onOutputBufferDrained: -32
06-13 11:20:27.890  1697 13725 E ACodec  : signalError(omxError 0x80001001, internalError -2147483648)
06-13 11:20:27.893  1697 13725 E Surface : queueBuffer: error queuing buffer, -32
06-13 11:20:27.893  1697 13725 E ACodec  : queueBuffer failed in onOutputBufferDrained: -32
06-13 11:20:27.894  1697 13725 E ACodec  : signalError(omxError 0x80001001, internalError -2147483648)
06-13 11:20:27.894  1697 13725 E Surface : queueBuffer: error queuing buffer, -32
06-13 11:20:27.894  1697 13725 E ACodec  : queueBuffer failed in onOutputBufferDrained: -32
06-13 11:20:27.894  1697 13725 E ACodec  : signalError(omxError 0x80001001, internalError -2147483648)
06-13 11:20:27.894 30112 30246 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:27.895 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:27.895 30112 30246 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:27.898  1697 13722 E MediaCodec: [TF-VIDEO]Codec reported err 0x80001001/-2147479551 (Unknown error 2147479551), actionCode 0, while in state 6/STARTED
06-13 11:20:27.899  1697 13722 E Surface : freeAllBuffers: 4 buffers were freed while being dequeued!
06-13 11:20:27.899  1697 13722 E SurfaceUtils: Failed to disconnect from surface 0xf1e60808, err -32
06-13 11:20:27.903  1697 13722 E MediaCodec: [TF-VIDEO]Codec reported err 0x80001001/-2147479551 (Unknown error 2147479551), actionCode 0, while in state 0/UNINITIALIZED
06-13 11:20:27.903  1697 13722 E MediaCodec: [TF-VIDEO]Codec reported err 0x80001001/-2147479551 (Unknown error 2147479551), actionCode 0, while in state 0/UNINITIALIZED
06-13 11:20:27.903  1697 13722 E MediaCodec: [TF-VIDEO]Codec reported err 0x80001001/-2147479551 (Unknown error 2147479551), actionCode 0, while in state 0/UNINITIALIZED
06-13 11:20:27.903  1697 13722 E MediaCodec: [TF-VIDEO]Codec reported err 0x80001001/-2147479551 (Unknown error 2147479551), actionCode 0, while in state 0/UNINITIALIZED
06-13 11:20:27.903  1697 13722 E MediaCodec: [TF-VIDEO]Codec reported err 0x80001001/-2147479551 (Unknown error 2147479551), actionCode 0, while in state 0/UNINITIALIZED
06-13 11:20:27.903  1697 13722 E MediaCodec: [TF-VIDEO]Codec reported err 0x80001001/-2147479551 (Unknown error 2147479551), actionCode 0, while in state 0/UNINITIALIZED
06-13 11:20:27.903  1697 13722 E MediaCodec: [TF-VIDEO]Codec reported err 0x80001001/-2147479551 (Unknown error 2147479551), actionCode 0, while in state 0/UNINITIALIZED
06-13 11:20:27.903  1697 13721 E NuPlayerDecoder: failed to release output buffer for [OMX.qcom.video.decoder.avc] (err=-38)
06-13 11:20:27.904  1697 13721 E NuPlayerDecoder: Decoder (video) reported error : 0x80001001
06-13 11:20:27.904  1697 13692 E NuPlayer: received error(0xffffffda) from video decoder, flushing(0), now shutting down
06-13 11:20:27.908  1697 13692 E NuPlayer: received error(0x80001001) from video decoder, flushing(2), now shutting down
06-13 11:20:27.910  1697 13721 E NuPlayerDecoder: failed to flush [OMX.qcom.video.decoder.avc] (err=-38)
06-13 11:20:27.911  1251  2113 E BpTransactionCompletedListener: Failed to transact (-32)
06-13 11:20:27.911  1697 13692 E NuPlayer: received error(0xffffffda) from video decoder, flushing(2), now shutting down
06-13 11:20:27.912 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=2
06-13 11:20:27.912 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8517   userId=0   baseIntent=Intent { act=android.intent.action.VIEW flag=335552512 cmp=ComponentInfo{com.android.chrome/org.chromium.chrome.browser.ChromeTabbedActivity} }
06-13 11:20:27.912 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8515   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.whatsapp/com.whatsapp.Main} }
06-13 11:20:27.932  1697 13725 E Surface : getSlotFromBufferLocked: unknown buffer: 0xf2682530
06-13 11:20:27.933  1697 13725 E Surface : getSlotFromBufferLocked: unknown buffer: 0xf26825a0
06-13 11:20:27.933  1697 13725 E Surface : getSlotFromBufferLocked: unknown buffer: 0xf2682610
06-13 11:20:27.933  1697 13725 E Surface : getSlotFromBufferLocked: unknown buffer: 0xf2682680
06-13 11:20:27.934 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:27.935  1697 13725 E Surface : getSlotFromBufferLocked: unknown buffer: 0xf2682760
06-13 11:20:27.935  1697 13725 E Surface : getSlotFromBufferLocked: unknown buffer: 0xf26827d0
06-13 11:20:27.935  1697 13725 E Surface : getSlotFromBufferLocked: unknown buffer: 0xf2682bc0
06-13 11:20:27.935 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=2
06-13 11:20:27.935  1697 13725 E Surface : getSlotFromBufferLocked: unknown buffer: 0xf2682c30
06-13 11:20:27.935 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8517   userId=0   baseIntent=Intent { act=android.intent.action.VIEW flag=335552512 cmp=ComponentInfo{com.android.chrome/org.chromium.chrome.browser.ChromeTabbedActivity} }
06-13 11:20:27.935 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8515   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.whatsapp/com.whatsapp.Main} }
06-13 11:20:27.936  1697 13725 E Surface : getSlotFromBufferLocked: unknown buffer: 0xf2682ca0
06-13 11:20:27.936  1697 13725 E Surface : getSlotFromBufferLocked: unknown buffer: 0xf2682d10
06-13 11:20:27.936  1697 13725 E Surface : getSlotFromBufferLocked: unknown buffer: 0xf2682d80
06-13 11:20:27.937  1697 13725 E Surface : getSlotFromBufferLocked: unknown buffer: 0xf2682df0
06-13 11:20:27.966 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:27.969 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=2
06-13 11:20:27.969 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8517   userId=0   baseIntent=Intent { act=android.intent.action.VIEW flag=335552512 cmp=ComponentInfo{com.android.chrome/org.chromium.chrome.browser.ChromeTabbedActivity} }
06-13 11:20:27.969 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8515   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.whatsapp/com.whatsapp.Main} }
06-13 11:20:27.970  1697 13721 E SurfaceUtils: Failed to connect to surface 0xf1e60808, err -32
06-13 11:20:27.976  1248 28329 E AudioFlingerImpl: open /proc/1697/cmdline error
06-13 11:20:27.977  1697 13674 E SurfaceUtils: Failed to disconnect from surface 0xf1145c08, err -32
06-13 11:20:27.977  1697 13673 E SurfaceUtils: Failed to connect to surface 0xf1145c08, err -32
06-13 11:20:27.985  1697 22178 E SurfaceUtils: Failed to disconnect from surface 0xf1147808, err -32
06-13 11:20:27.992  1248 27218 E AudioFlingerImpl: open /proc/1697/cmdline error
06-13 11:20:27.998  1697 22009 E SurfaceUtils: Failed to disconnect from surface 0xf1e5ec08, err -32
06-13 11:20:28.002  1248 28329 E AudioFlingerImpl: open /proc/1697/cmdline error
06-13 11:20:28.002  2032  2908 E ProcessManager: error write exception log
06-13 11:20:28.009  2032  6068 E TaskPersister: File error accessing recents directory (directory doesn't exist?).
06-13 11:20:28.010 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.013 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=1
06-13 11:20:28.013 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8515   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.whatsapp/com.whatsapp.Main} }
06-13 11:20:28.050  1697 13669 E SurfaceUtils: Failed to disconnect from surface 0xf1e5d008, err -32
06-13 11:20:28.051  1697 13668 E SurfaceUtils: Failed to connect to surface 0xf1e5d008, err -32
06-13 11:20:28.057  1697 22178 E SurfaceUtils: Failed to disconnect from surface 0xf1149408, err -32
06-13 11:20:28.074  1109  1282 E ANDR-PERF-OPTSHANDLER: Perflock resource /sys/class/devfreq/soc:qcom,cpu-llcc-ddr-bw/min_freq not supported
06-13 11:20:28.074  1109  1282 E ANDR-PERF-RESOURCEQS: Failed to apply optimization [12, 4]
06-13 11:20:28.099 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.102 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=1
06-13 11:20:28.102 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8515   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.whatsapp/com.whatsapp.Main} }
06-13 11:20:28.102 30112 30246 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.103 30112 30246 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.105 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.108 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=1
06-13 11:20:28.108 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8515   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.whatsapp/com.whatsapp.Main} }
06-13 11:20:28.109  1248  1771 E AudioPolicyService: AudioCommandThread() sending outputdevice changed(id=2)  to 0
06-13 11:20:28.113  2032  2908 E ProcessManager: error write exception log
06-13 11:20:28.116  1053  1244 E DlbEffectContext: setParam() received unknown parameter 20
06-13 11:20:28.118  1248  1771 E AudioPolicyService: AudioCommandThread() processing output device changed 0 2
06-13 11:20:28.119  2032  4482 E AudioSystem: onAudioOutputDeviceChanged (0, 2)
06-13 11:20:28.119  3305  4172 E AudioSystem: onAudioOutputDeviceChanged (0, 2)
06-13 11:20:28.119 21440  5110 E AudioSystem: onAudioOutputDeviceChanged (0, 2)
06-13 11:20:28.119  3301  3959 E AudioSystem: onAudioOutputDeviceChanged (0, 2)
06-13 11:20:28.119 10242 14401 E AudioSystem: onAudioOutputDeviceChanged (0, 2)
06-13 11:20:28.119  1248  1771 E AudioSystem: onAudioOutputDeviceChanged (0, 2)
06-13 11:20:28.119 30217 31101 E AudioSystem: onAudioOutputDeviceChanged (0, 2)
06-13 11:20:28.119  1697 22178 E AudioSystem: onAudioOutputDeviceChanged (0, 2)
06-13 11:20:28.119 26284 26711 E AudioSystem: onAudioOutputDeviceChanged (0, 2)
06-13 11:20:28.135  2032  2908 E ProcessManager: error write exception log
06-13 11:20:28.233 21212 21212 E StateManager: setWorkspaceProperty  state=LauncherState
06-13 11:20:28.233 21212 21212 E StateManager: setShortcutMenuLayerProperty  state=LauncherState   alpha=1.0   scale=1.0
06-13 11:20:28.233 21212 21212 E RecentsViewStateController: setState: state=LauncherState   mIsIgnoreOverviewAnim=false
06-13 11:20:28.236 21212 21212 E RecentsViewStateController: set RecentsContainer GONE when setState with animation,  need:true
06-13 11:20:28.236 21212 21212 E BlurUtils: fastBlur ratio=0.0   withAnim=true   sBlurRatioValue=1.0
06-13 11:20:28.238 21212 21212 E OverviewState: onExitState
06-13 11:20:28.238 21212 21212 E RotationHelper: setCurrentStateRequest: request=0
06-13 11:20:28.238 21212 21212 E RotationHelper: notifyChange activityFlags=1, mMultiWindowRotationRequest=3, mCurrentTransitionRequest=0, mCurrentStateRequest=0
06-13 11:20:28.245  2032  6068 E TaskPersister: File error accessing recents directory (directory doesn't exist?).
06-13 11:20:28.254 21212 21212 E BlurUtils: fastBlurDirectly ratio=0.98992753   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:28.254 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.257 21212 21315 E Launcher: notifyBackGestureStatus:run usingFsGesture=true   show=true   focus=true   pause=false
06-13 11:20:28.259 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.287 21212 21212 E BlurUtils: fastBlurDirectly ratio=0.9081696   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:28.308  1109  1282 E ANDR-PERF-OPTSHANDLER: Perflock resource /sys/class/devfreq/soc:qcom,cpu-llcc-ddr-bw/min_freq not supported
06-13 11:20:28.308  1109  1282 E ANDR-PERF-RESOURCEQS: Failed to apply optimization [12, 4]
06-13 11:20:28.320 21212 21212 E BlurUtils: fastBlurDirectly ratio=0.7572197   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:28.332  1109  4500 E ANDR-PERF-LM: VmRssMeter:: start() 447: Could not find pid, can not collect vmrss data
06-13 11:20:28.333 21212 21212 E Launcher.Boost: boost failed
06-13 11:20:28.333 21212 21212 E Launcher.Boost: java.lang.NoSuchMethodError: No static method setThreadPriority(IILjava/lang/String;)V in class Landroid/os/MiuiProcess; or its super classes (declaration of 'android.os.MiuiProcess' appears in /system_ext/framework/miui-framework.jar)
06-13 11:20:28.333 21212 21212 E Launcher.Boost: 	at com.miui.launcher.utils.BoostHelper.boost(BoostHelper.java:85)
06-13 11:20:28.333 21212 21212 E Launcher.Boost: 	at com.miui.home.launcher.Launcher$59.run(Launcher.java:7000)
06-13 11:20:28.333 21212 21212 E Launcher.Boost: 	at android.os.Handler.handleCallback(Handler.java:942)
06-13 11:20:28.333 21212 21212 E Launcher.Boost: 	at android.os.Handler.dispatchMessage(Handler.java:99)
06-13 11:20:28.333 21212 21212 E Launcher.Boost: 	at android.os.Looper.loopOnce(Looper.java:211)
06-13 11:20:28.333 21212 21212 E Launcher.Boost: 	at android.os.Looper.loop(Looper.java:300)
06-13 11:20:28.333 21212 21212 E Launcher.Boost: 	at android.app.ActivityThread.main(ActivityThread.java:8503)
06-13 11:20:28.333 21212 21212 E Launcher.Boost: 	at java.lang.reflect.Method.invoke(Native Method)
06-13 11:20:28.333 21212 21212 E Launcher.Boost: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:561)
06-13 11:20:28.333 21212 21212 E Launcher.Boost: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:954)
06-13 11:20:28.334  1109  4500 E ANDR-PERF-LM: AdaptiveLaunch: writeToDataBase() 64: Meter aborted or could not get meter data for this run
06-13 11:20:28.343  1109  4499 E ANDR-PERF-LM: VmRssMeter:: start() 447: Could not find pid, can not collect vmrss data
06-13 11:20:28.346 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.347 30112 30246 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.347 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.348 30112 30246 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.349 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.350 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.354 21212 21212 E BlurUtils: fastBlurDirectly ratio=0.56266654   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:28.356  2032  2908 E ProcessManager: error write exception log
06-13 11:20:28.377 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.380 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.386 21212 21212 E BlurUtils: fastBlurDirectly ratio=0.3514793   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:28.420 21212 21212 E BlurUtils: fastBlurDirectly ratio=0.17170721   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:28.424  2032  2908 E ProcessManager: error write exception log
06-13 11:20:28.436 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.441 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.453 21212 21212 E BlurUtils: fastBlurDirectly ratio=0.04758644   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:28.486 21212 21212 E BlurUtils: fastBlurDirectly ratio=1.578927E-4   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:28.502 21212 21212 E BlurUtils: fastBlurDirectly ratio=0.0   win=com.android.internal.policy.PhoneWindow@f7b28fd
06-13 11:20:28.542  2032  2908 E ProcessManager: error write exception log
06-13 11:20:28.560 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.563 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:28.751 21212 21212 E RotationHelper: setCurrentStateRequest: request=0
06-13 11:20:29.163  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-13 11:20:29.362  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-13 11:20:29.405 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.405 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.405 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.405 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.405 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.405 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.407 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.407 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.407 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.407 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.407 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.407 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.407 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.407 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.407 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.409 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.409 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.409 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.409 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.409 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.409 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.409 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.409 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.409 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.409 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.409 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.410 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.411 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.411 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.411 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.411 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.411 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.411 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.414 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.414 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.415 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.419 13780 13822 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.733  2032  6215 E AppOps  : Bad call made by uid 10189. Package "com.google.android.gms" does not belong to uid 10209.
06-13 11:20:29.734 13780 13876 E GoogleApiManager: Failed to get service from broker. 
06-13 11:20:29.734 13780 13876 E GoogleApiManager: java.lang.SecurityException: Unknown calling package name 'com.google.android.gms'.
06-13 11:20:29.734 13780 13876 E GoogleApiManager: 	at android.os.Parcel.createExceptionOrNull(Parcel.java:3011)
06-13 11:20:29.734 13780 13876 E GoogleApiManager: 	at android.os.Parcel.createException(Parcel.java:2995)
06-13 11:20:29.734 13780 13876 E GoogleApiManager: 	at android.os.Parcel.readException(Parcel.java:2978)
06-13 11:20:29.734 13780 13876 E GoogleApiManager: 	at android.os.Parcel.readException(Parcel.java:2920)
06-13 11:20:29.734 13780 13876 E GoogleApiManager: 	at axtl.a(:com.google.android.gms@252234029@25.22.34 (190400-769260661):36)
06-13 11:20:29.734 13780 13876 E GoogleApiManager: 	at axrs.z(:com.google.android.gms@252234029@25.22.34 (190400-769260661):143)
06-13 11:20:29.734 13780 13876 E GoogleApiManager: 	at awyv.run(:com.google.android.gms@252234029@25.22.34 (190400-769260661):42)
06-13 11:20:29.734 13780 13876 E GoogleApiManager: 	at android.os.Handler.handleCallback(Handler.java:942)
06-13 11:20:29.734 13780 13876 E GoogleApiManager: 	at android.os.Handler.dispatchMessage(Handler.java:99)
06-13 11:20:29.734 13780 13876 E GoogleApiManager: 	at chad.mH(:com.google.android.gms@252234029@25.22.34 (190400-769260661):1)
06-13 11:20:29.734 13780 13876 E GoogleApiManager: 	at chad.dispatchMessage(:com.google.android.gms@252234029@25.22.34 (190400-769260661):5)
06-13 11:20:29.734 13780 13876 E GoogleApiManager: 	at android.os.Looper.loopOnce(Looper.java:211)
06-13 11:20:29.734 13780 13876 E GoogleApiManager: 	at android.os.Looper.loop(Looper.java:300)
06-13 11:20:29.734 13780 13876 E GoogleApiManager: 	at android.os.HandlerThread.run(HandlerThread.java:67)
06-13 11:20:29.930 13887 13887 E Zygote  : process_name_ptr:13887 com.whatsapp
06-13 11:20:29.937 13887 13887 E com.whatsapp: Not starting debugger since process cannot load the jdwp agent.
06-13 11:20:29.938 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.938 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.939 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.939 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.939 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.939 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.939 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.939 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.939 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.940 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.940 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.940 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.940 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.940 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.940 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.940 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.940 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.941 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.941 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.942 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.942 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.942 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.943 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.943 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.943 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.943 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.943 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.948 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:29.948 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.207 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.207 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.207 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.207 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.207 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.207 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.207 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.207 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.208 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.208 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.208 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.208 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.208 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.208 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.208 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.208 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.208 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.208 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.208 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.208 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.208 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.209 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.209 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.209 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.209 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.209 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.209 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.213 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.213 13780 13845 E PhFileGroupPop: Failed to add file group 
06-13 11:20:30.798 13887 13917 E SQLiteLog: (1) duplicate column name: mute_reactions in "ALTER TABLE settings ADD mute_reactions INTEGER"
06-13 11:20:31.140  1053 26048 E send_data_to_xlog: get_int_by_key: unable to get property persist.vendor.audio.scenario
06-13 11:20:31.142  1053 26048 E soundtrigger: audio_extn_sound_trigger_update_stream_status: invalid input device 0x0, for event 2
06-13 11:20:31.163  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-13 11:20:31.283  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-13 11:20:31.647  9170 13630 E FbnsAIDLClientManager: Max Try reached for binding to FbnsAIDLService, threadId 36715
06-13 11:20:31.666  9170 13630 E FbnsAIDLClientManager: Fbns AIDL request got RemoteException
06-13 11:20:31.666  9170 13630 E FbnsAIDLClientManager: android.os.RemoteException: AIDLService is not bound
06-13 11:20:31.666  9170 13630 E FbnsAIDLClientManager: 	at X.5oL.A00(:21)
06-13 11:20:31.666  9170 13630 E FbnsAIDLClientManager: 	at X.BQb.call(:302)
06-13 11:20:31.666  9170 13630 E FbnsAIDLClientManager: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
06-13 11:20:31.666  9170 13630 E FbnsAIDLClientManager: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
06-13 11:20:31.666  9170 13630 E FbnsAIDLClientManager: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
06-13 11:20:31.666  9170 13630 E FbnsAIDLClientManager: 	at java.lang.Thread.run(Thread.java:1012)
06-13 11:20:32.373  2032  6068 E TaskPersister: File error accessing recents directory (directory doesn't exist?).
06-13 11:20:34.834 21212 21212 E StateManager: setWorkspaceProperty  state=AllAppsState
06-13 11:20:34.838 21212 21212 E StateManager: setShortcutMenuLayerProperty  state=AllAppsState   alpha=1.0   scale=1.0
06-13 11:20:34.838 21212 21212 E RecentsViewStateController: setState: state=AllAppsState   mIsIgnoreOverviewAnim=false
06-13 11:20:34.840 21212 21212 E RotationHelper: setCurrentStateRequest: request=0
06-13 11:20:34.844 21212 21212 E BranchAllAppsIndicator: changedByBranch: callBack = null
06-13 11:20:34.845 21212 21315 E Launcher: notifyBackGestureStatus:run usingFsGesture=true   show=true   focus=true   pause=false
06-13 11:20:34.849 21212 21212 E BranchAllAppsIndicator: changedByBranch: callBack = null
06-13 11:20:34.935 21212 21212 E RotationHelper: setCurrentStateRequest: request=0
06-13 11:20:35.165  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-13 11:20:35.183 21212 21212 E RotationHelper: setCurrentStateRequest: request=0
06-13 11:20:35.686  1189  3557 E QSEECOMAPI: Error::Cannot de-alloc memory. priv handle is NULL!!.
06-13 11:20:35.717  1189  3556 E QSEECOMAPI: Error::Cannot de-alloc memory. priv handle is NULL!!.
06-13 11:20:35.750  1189  3556 E QSEECOMAPI: Error::Cannot de-alloc memory. priv handle is NULL!!.
06-13 11:20:35.865 21212 21212 E QuickstepAppTransitionManagerImpl: getActivityLaunchOptions iconLoc=Rect(51, 956 - 219, 1124)
06-13 11:20:35.909  1109  1283 E ANDR-PERF-LM: MetaMeter: handleAsync() 166: AdaptLaunch Invalid Async
06-13 11:20:35.910  1109  1283 E ANDR-PERF-LM: MetaMeter: handleAsync() 166: AdaptLaunch Invalid Async
06-13 11:20:35.911  2032  3378 E ANDR-PERF-JNI: com_qualcomm_qtiperformance_native_perf_io_prefetch_start
06-13 11:20:35.929  1100  4471 E ANDR-IOP: io prefetch is disabled
06-13 11:20:35.954  2032  2666 E MiuiActivityController: MiuiLog-ActivityObserver: There was something wrong : null
06-13 11:20:35.955  1109  1283 E ANDR-PERF-LM: MetaMeter: handleAsync() 166: AdaptLaunch Invalid Async
06-13 11:20:35.955 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-13 11:20:35.958 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=1
06-13 11:20:35.958 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8520   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.aegis.systemmonitor/com.aegis.systemmonitor.MainActivity} }
06-13 11:20:35.978 14006 14006 E Zygote  : process_name_ptr:14006 com.android.vending
06-13 11:20:35.988 14004 14004 E Zygote  : process_name_ptr:14004 com.aegis.systemmonitor
06-13 11:20:35.995 14006 14006 E android.vending: Not starting debugger since process cannot load the jdwp agent.
06-13 11:20:35.998  3305  3655 E perf_hint: Session creation failed, mPreferredRateNanos: -1
06-13 11:20:35.998 14004 14004 E s.systemmonitor: Not starting debugger since process cannot load the jdwp agent.
06-13 11:20:36.022 21212 21227 E LauncherAnimationRunner: onAnimationStart
06-13 11:20:36.022 21212 21227 E LauncherAnimationRunner: onAnimationStart:   mode=1   taskId=2   isTranslucent=false   activityType=2
06-13 11:20:36.022 21212 21227 E LauncherAnimationRunner: onAnimationStart:   mode=0   taskId=8520   isTranslucent=false   activityType=1
06-13 11:20:36.022 21212 21212 E QuickstepAppTransitionManagerImpl: startIconLaunchAnimator:launcherClosing=true   iconLoc=Rect(51, 956 - 219, 1124)
06-13 11:20:36.022 21212 21212 E QuickstepAppTransitionManagerImpl: startOpeningWindowAnimators:iconLoc=Rect(51, 956 - 219, 1124)
06-13 11:20:36.022 21212 21212 E ClipAnimationHelper: updateSourceStack  mSourceInsets=Rect(0, 80 - 0, 44), mSourceStackBounds=Rect(0, 0 - 1080, 2400)
06-13 11:20:36.022 21212 21212 E ClipAnimationHelper: updateHomeStack  mSourceInsets=Rect(0, 80 - 0, 44), mHomeStackBounds=Rect(0, 0 - 1080, 2400)
06-13 11:20:36.022 21212 21212 E ClipAnimationHelper: updateTargetRect  mSourceRect=RectF(0.0, 80.0, 1080.0, 2480.0)   mTargetRect=RectF(0.0, 0.0, 1080.0, 2400.0)   mSourceWindowClipInsets=RectF(0.0, 80.0, 0.0, 0.0)   mHomeStackBounds=Rect(0, 0 - 1080, 2400)   targetRect=Rect(0, 0 - 1080, 2400)
06-13 11:20:36.024  1109  1283 E ANDR-PERF-LM: MetaMeter: handleAsync() 166: AdaptLaunch Invalid Async
06-13 11:20:36.024 21212 21212 E QuickstepAppTransitionManagerImpl: startLauncherContentAnimator:isAppOpening=true
06-13 11:20:36.035 21212 21461 E perf_hint: Session creation failed, mPreferredRateNanos: -1
06-13 11:20:36.057 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 8520
06-13 11:20:36.057 30112 30246 E ActivityManagerWrapper: get all recent tasks force including 8520
06-13 11:20:36.063 30112 30246 E ActivityManagerWrapper: get all recent tasks force including 8520
06-13 11:20:36.063 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=1
06-13 11:20:36.063 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8520   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.aegis.systemmonitor/com.aegis.systemmonitor.MainActivity} }
06-13 11:20:36.074 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 8520
06-13 11:20:36.078 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=1
06-13 11:20:36.079 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8520   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.aegis.systemmonitor/com.aegis.systemmonitor.MainActivity} }
06-13 11:20:36.455  2032  2908 E ProcessManager: error write exception log
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: Failure in reading rpm stats
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: java.io.FileNotFoundException: /sys/power/system_sleep/stats: open failed: EACCES (Permission denied)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at libcore.io.IoBridge.open(IoBridge.java:574)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at java.io.FileInputStream.<init>(FileInputStream.java:179)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at java.io.FileInputStream.<init>(FileInputStream.java:133)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at com.miui.powerkeeper.utils.KernelRpmStatsReader.readRpmStatsType1(Unknown Source:19)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at com.miui.powerkeeper.utils.KernelRpmStatsReader.readRpmStats(Unknown Source:63)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at com.miui.powerkeeper.powerchecker.PowerCheckerService.requestKernelRpmStatsUpdate(Unknown Source:4)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at com.miui.powerkeeper.powerchecker.PowerCheckerService.addBatteryHistoryRecord(Unknown Source:32)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at com.miui.powerkeeper.powerchecker.PowerCheckerService.updateBatteryLevelChanged(Unknown Source:8)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at com.miui.powerkeeper.powerchecker.PowerCheckerService.access$2700(Unknown Source:0)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at com.miui.powerkeeper.powerchecker.PowerCheckerService$PowerCheckHandler.handleMessage(Unknown Source:124)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at android.os.Handler.dispatchMessage(Handler.java:106)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at android.os.Looper.loopOnce(Looper.java:211)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at android.os.Looper.loop(Looper.java:300)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at android.os.HandlerThread.run(HandlerThread.java:67)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: Caused by: android.system.ErrnoException: open failed: EACCES (Permission denied)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at libcore.io.Linux.open(Native Method)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at libcore.io.ForwardingOs.open(ForwardingOs.java:563)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at libcore.io.BlockGuardOs.open(BlockGuardOs.java:274)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at libcore.io.ForwardingOs.open(ForwardingOs.java:563)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at android.app.ActivityThread$AndroidOs.open(ActivityThread.java:8381)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	at libcore.io.IoBridge.open(IoBridge.java:560)
06-13 11:20:36.484  7993  8153 E KernelRpmStatsReader: 	... 13 more
06-13 11:20:36.573 21212 21212 E LauncherAnimationRunner: finish: mFinishRunnable.run
06-13 11:20:36.716 14004 14049 E libEGL  : pre_cache appList: ,,,
06-13 11:20:36.930 14152 14152 E Zygote  : process_name_ptr:14152 com.android.vending:background
06-13 11:20:36.935 14152 14152 E ding:background: Not starting debugger since process cannot load the jdwp agent.
06-13 11:20:36.959  1109  4499 E ANDR-PERF-LM: VmRssMeter:: start() 447: Could not find pid, can not collect vmrss data
06-13 11:20:36.959  1109  4500 E ANDR-PERF-LM: VmRssMeter:: start() 447: Could not find pid, can not collect vmrss data
06-13 11:20:36.959  1109  4500 E ANDR-PERF-LM: AdaptiveLaunch: writeToDataBase() 64: Meter aborted or could not get meter data for this run
06-13 11:20:37.346 25633 14206 E RoleControllerServiceImpl: Default/fallback role holder package doesn't qualify for the role, package: com.google.android.gms.supervision, role: android.app.role.SYSTEM_SUPERVISION
06-13 11:20:37.419 21937 21937 E RegisteredServicesCache: Next Tag=services
06-13 11:20:37.419 21937 21937 E RegisteredServicesCache: installedServices no uidString 
06-13 11:20:37.419 21937 21937 E RegisteredServicesCache: installedServices no uidString 
06-13 11:20:37.419 21937 21937 E RegisteredServicesCache: installedServices contains uidString : 10189
06-13 11:20:37.419 21937 21937 E RegisteredServicesCache: installedServices contains uidString : 10189
06-13 11:20:37.419 21937 21937 E RegisteredServicesCache: 1invalidateCache:WriteServiceStateToFile
06-13 11:20:37.419 21937 21937 E RegisteredServicesCache:  Writing service state Data Always
06-13 11:20:37.422 21937 21937 E RegisteredServicesCache: component namecom.google.android.gms/com.google.android.gms.nearby.mediums.nearfieldcommunication.NfcAdvertisingService
06-13 11:20:37.422 21937 21937 E RegisteredServicesCache: component namecom.edenred.TicketRestaurant/com.path.ticket.nfc.TicketNfcService
06-13 11:20:37.422 21937 21937 E RegisteredServicesCache: component namecom.google.android.gms/com.google.android.gms.pay.hce.service.PayHceService
06-13 11:20:37.422 21937 21937 E RegisteredServicesCache: component namecom.google.android.gms/com.google.android.gms.dck.service.DckNfcApduService
06-13 11:20:37.458 21937 21937 E AidRoutingManager: Size of routing table970
06-13 11:20:37.587 17136 17317 E MiPicks-ModuleInterceptor: isOperatorSdkInited operator:default  status:null
06-13 11:20:37.595 17136 17317 E MiPicks-ModuleInterceptor: isOperatorSdkInited operator:default  status:null
06-13 11:20:37.646 14004 14049 E libboost: fail to open node: No such file or directory
06-13 11:20:37.661 14004 14049 E libEGL  : pre_cache appList: ,,,
06-13 11:20:37.704  2032  2181 E VerityUtils: Failed to measure fs-verity, errno 1: /data/app/~~2JiMj1Vj64Qi-AHygbmqaw==/com.aegis.systemmonitor-VlZRQH_B9h1aFsqrhV3blA==/base.apk
06-13 11:20:37.777 14004 14049 E perf_hint: Session creation failed, mPreferredRateNanos: -1
06-13 11:20:37.831  2032  2666 E MiuiActivityController: MiuiLog-ActivityObserver: There was something wrong : null
06-13 11:20:37.856  1123  1123 E DisplayFeatureHal: setFeatureEnable: failed to set fps(0)
06-13 11:20:37.874 14309 14309 E Zygote  : process_name_ptr:14309 com.google.android.webview:webview_service
06-13 11:20:37.883 14309 14309 E webview_service: Not starting debugger since process cannot load the jdwp agent.
06-13 11:20:38.042 14347 14347 E libc    : SetHeapTaggingLevel: re-enabling tagging after it was disabled is not supported
06-13 11:20:38.048 14347 14347 E ocessService0:0: Not starting debugger since process cannot load the jdwp agent.
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: Exception while idling and processing messages
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: java.net.SocketException: Socket closed
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at java.net.SocketInputStream.read(SocketInputStream.java:188)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at java.net.SocketInputStream.read(SocketInputStream.java:143)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:989)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:953)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:868)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:841)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at org.apache.http.impl.io.AbstractSessionInputBuffer.fillBuffer(AbstractSessionInputBuffer.java:108)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at org.apache.http.impl.io.AbstractSessionInputBuffer.readLine(AbstractSessionInputBuffer.java:196)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at org.apache.http.impl.conn.DefaultResponseParser.parseHead(DefaultResponseParser.java:88)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:179)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at org.apache.http.impl.AbstractHttpClientConnection.receiveResponseHeader(AbstractHttpClientConnection.java:185)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at org.apache.http.impl.conn.DefaultClientConnection.receiveResponseHeader(DefaultClientConnection.java:240)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at org.apache.http.impl.conn.AbstractClientConnAdapter.receiveResponseHeader(AbstractClientConnAdapter.java:264)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:284)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:126)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:442)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:573)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:502)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:478)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at vg.c.b(Unknown Source:75)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at vg.c.a(Unknown Source:9)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at vg.c.call(Unknown Source:0)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
06-13 11:20:38.072 25637  8482 E WS1_AWCMEngine: 	at java.lang.Thread.run(Thread.java:1012)
06-13 11:20:38.081 25637  8481 E WS1_AWCMClient: Exception occured while submiting the callable job / waiting for the callable's result
06-13 11:20:38.081 25637  8481 E WS1_AWCMClient: java.lang.InterruptedException
06-13 11:20:38.081 25637  8481 E WS1_AWCMClient: 	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:418)
06-13 11:20:38.081 25637  8481 E WS1_AWCMClient: 	at java.util.concurrent.FutureTask.get(FutureTask.java:190)
06-13 11:20:38.081 25637  8481 E WS1_AWCMClient: 	at vg.a.i(Unknown Source:79)
06-13 11:20:38.081 25637  8481 E WS1_AWCMClient: 	at vg.a.run(Unknown Source:0)
06-13 11:20:38.081 25637  8481 E WS1_AWCMClient: 	at java.lang.Thread.run(Thread.java:1012)
06-13 11:20:38.088 14347 14347 E MQSEventManagerDelegate: failed to get MQSService.
06-13 11:20:38.127 14347 14347 E ForceDarkHelperStubImpl: getForceDarkOriginState transact failed , mService: null
06-13 11:20:38.445 14004 14290 E s.systemmonitor: Attempt to load writable dex file: /data/user/0/com.aegis.systemmonitor/app_pccache/5/3964689533889E2A42BF9B701859475ECCE2EDB3/pcam.jar
06-13 11:20:38.470 14415 14415 E Zygote  : process_name_ptr:14415 com.android.htmlviewer:remote
06-13 11:20:38.483 14415 14415 E mlviewer:remote: Not starting debugger since process cannot load the jdwp agent.
06-13 11:20:38.510  2032  6224 E AppOps  : Bad call made by uid 10185. Package "com.google.android.gms" does not belong to uid 10185.
06-13 11:20:38.516  2032  2666 E MiuiActivityController: MiuiLog-ActivityObserver: There was something wrong : null
06-13 11:20:38.558 14004 14337 E Surface : freeAllBuffers: 1 buffers were freed while being dequeued!
06-13 11:20:38.565 14004 14337 E Surface : getSlotFromBufferLocked: unknown buffer: 0xb400007c592e6600
06-13 11:20:38.732 14004 14057 E ReactNativeJS: TypeError: Cannot read property 'trim' of undefined
06-13 11:20:38.783 14004 14057 E ReactNativeJS: TypeError: Cannot read property 'trim' of undefined
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: Unhandled SoftException
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: com.facebook.react.bridge.ReactNoCrashSoftException: raiseSoftException(getOrCreateDestroyTask()): handleHostException(message = "TypeError: Cannot read property 'trim' of undefined, stack:
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: onFirstUse@1:1681734
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: anonymous@1:1682385
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: setProgram@1:1881218
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: anonymous@1:1884810
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: renderObject@1:1878627
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: renderObjects@1:1878407
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: renderScene@1:1877380
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: anonymous@1:1887351
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: anonymous@1:1581729
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: render$1@1:1596667
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: loop@1:1597036
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: ")
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl.raiseSoftException(ReactHostImpl.java:942)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl.getOrCreateDestroyTask(ReactHostImpl.java:1575)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl.lambda$destroy$7(ReactHostImpl.java:541)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl.$r8$lambda$uso21_D6dCZdcf-JomVD56kdG4c(Unknown Source:0)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl$$ExternalSyntheticLambda37.call(D8$$SyntheticClass:0)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at com.facebook.react.runtime.internal.bolts.Task$2.run(Task.java:240)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at java.lang.Thread.run(Thread.java:1012)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: Caused by: com.facebook.react.common.JavascriptException: TypeError: Cannot read property 'trim' of undefined, stack:
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: onFirstUse@1:1681734
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: anonymous@1:1682385
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: setProgram@1:1881218
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: anonymous@1:1884810
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: renderObject@1:1878627
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: renderObjects@1:1878407
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: renderScene@1:1877380
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: anonymous@1:1887351
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: anonymous@1:1581729
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: render$1@1:1596667
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: loop@1:1597036
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at com.facebook.react.modules.core.ExceptionsManagerModule.reportException(ExceptionsManagerModule.kt:52)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at com.facebook.jni.NativeRunnable.run(Native Method)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at android.os.Handler.handleCallback(Handler.java:942)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at android.os.Handler.dispatchMessage(Handler.java:99)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at com.facebook.react.bridge.queue.MessageQueueThreadHandler.dispatchMessage(MessageQueueThreadHandler.java:27)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at android.os.Looper.loopOnce(Looper.java:211)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at android.os.Looper.loop(Looper.java:300)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at com.facebook.react.bridge.queue.MessageQueueThreadImpl.lambda$startNewBackgroundThread$2(MessageQueueThreadImpl.java:217)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	at com.facebook.react.bridge.queue.MessageQueueThreadImpl$$ExternalSyntheticLambda1.run(D8$$SyntheticClass:0)
06-13 11:20:38.786 14004 14056 E unknown:ReactHost: 	... 1 more
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: Unhandled SoftException
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: com.facebook.react.bridge.ReactNoCrashSoftException: raiseSoftException(getOrCreateDestroyTask()): handleHostException(message = "TypeError: Cannot read property 'trim' of undefined, stack:
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: onFirstUse@1:1681734
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: anonymous@1:1682385
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: setProgram@1:1881218
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: anonymous@1:1884810
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: renderObject@1:1878627
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: renderObjects@1:1878407
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: renderScene@1:1877380
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: anonymous@1:1887351
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: anonymous@1:1581729
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: render$1@1:1596667
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: loop@1:1597036
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: ")
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl.raiseSoftException(ReactHostImpl.java:942)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl.getOrCreateDestroyTask(ReactHostImpl.java:1575)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl.lambda$destroy$7(ReactHostImpl.java:541)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl.$r8$lambda$uso21_D6dCZdcf-JomVD56kdG4c(Unknown Source:0)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl$$ExternalSyntheticLambda37.call(D8$$SyntheticClass:0)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at com.facebook.react.runtime.internal.bolts.Task$2.run(Task.java:240)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at java.lang.Thread.run(Thread.java:1012)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: Caused by: com.facebook.react.common.JavascriptException: TypeError: Cannot read property 'trim' of undefined, stack:
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: onFirstUse@1:1681734
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: anonymous@1:1682385
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: setProgram@1:1881218
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: anonymous@1:1884810
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: renderObject@1:1878627
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: renderObjects@1:1878407
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: renderScene@1:1877380
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: anonymous@1:1887351
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: anonymous@1:1581729
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: render$1@1:1596667
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: loop@1:1597036
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at com.facebook.react.modules.core.ExceptionsManagerModule.reportException(ExceptionsManagerModule.kt:52)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at com.facebook.jni.NativeRunnable.run(Native Method)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at android.os.Handler.handleCallback(Handler.java:942)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at android.os.Handler.dispatchMessage(Handler.java:99)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at com.facebook.react.bridge.queue.MessageQueueThreadHandler.dispatchMessage(MessageQueueThreadHandler.java:27)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at android.os.Looper.loopOnce(Looper.java:211)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at android.os.Looper.loop(Looper.java:300)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at com.facebook.react.bridge.queue.MessageQueueThreadImpl.lambda$startNewBackgroundThread$2(MessageQueueThreadImpl.java:217)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	at com.facebook.react.bridge.queue.MessageQueueThreadImpl$$ExternalSyntheticLambda1.run(D8$$SyntheticClass:0)
06-13 11:20:38.789 14004 14056 E unknown:ReactHost: 	... 1 more
06-13 11:20:38.801 14004 14004 E unknown:ReactNative: Tried to remove non-existent frame callback
06-13 11:20:38.801 14004 14056 E unknown:SurfaceMountingManager: Stopping surface [11]
06-13 11:20:38.814 14004 14004 E unknown:SurfaceMountingManager: Surface [11] was stopped on SurfaceMountingManager.
06-13 11:20:38.929  1685  7357 E installd: Failed to set flags for file:/data/user/0/com.aegis.systemmonitor/app_pccache/5/3964689533889E2A42BF9B701859475ECCE2EDB3/oat/arm64/pcam.odex.: Operation not supported on transport endpoint
06-13 11:20:38.929  1685  7357 E installd: Failed to set flags for file:/data/user/0/com.aegis.systemmonitor/app_pccache/5/3964689533889E2A42BF9B701859475ECCE2EDB3/oat/arm64/pcam.vdex.: Operation not supported on transport endpoint
06-13 11:20:38.948 17136 17251 E MiPicks-ConnectionRSA: get key exception : com.android.org.bouncycastle.jcajce.provider.util.BadBlockException: unable to decrypt block
06-13 11:20:38.954  2032  6068 E TaskPersister: File error accessing recents directory (directory doesn't exist?).
06-13 11:20:39.000 14004 14057 E ReactNativeJS: TypeError: Cannot read property 'trim' of undefined
06-13 11:20:39.074  2032 20556 E DexoptServiceThread: Remove dexopt pid 14457 and cancelled is false
06-13 11:20:39.135 14004 14056 E unknown:ReactNative: Tried to remove non-existent frame callback
06-13 11:20:39.167  2032  2908 E ProcessManager: error write exception log
06-13 11:20:39.722  2032  6212 E AppOps  : Bad call made by uid 10189. Package "com.google.android.gms" does not belong to uid 10209.
06-13 11:20:39.728 13780 13876 E GoogleApiManager: Failed to get service from broker. 
06-13 11:20:39.728 13780 13876 E GoogleApiManager: java.lang.SecurityException: Unknown calling package name 'com.google.android.gms'.
06-13 11:20:39.728 13780 13876 E GoogleApiManager: 	at android.os.Parcel.createExceptionOrNull(Parcel.java:3011)
06-13 11:20:39.728 13780 13876 E GoogleApiManager: 	at android.os.Parcel.createException(Parcel.java:2995)
06-13 11:20:39.728 13780 13876 E GoogleApiManager: 	at android.os.Parcel.readException(Parcel.java:2978)
06-13 11:20:39.728 13780 13876 E GoogleApiManager: 	at android.os.Parcel.readException(Parcel.java:2920)
06-13 11:20:39.728 13780 13876 E GoogleApiManager: 	at axtl.a(:com.google.android.gms@252234029@25.22.34 (190400-769260661):36)
06-13 11:20:39.728 13780 13876 E GoogleApiManager: 	at axrs.z(:com.google.android.gms@252234029@25.22.34 (190400-769260661):143)
06-13 11:20:39.728 13780 13876 E GoogleApiManager: 	at awyv.run(:com.google.android.gms@252234029@25.22.34 (190400-769260661):42)
06-13 11:20:39.728 13780 13876 E GoogleApiManager: 	at android.os.Handler.handleCallback(Handler.java:942)
06-13 11:20:39.728 13780 13876 E GoogleApiManager: 	at android.os.Handler.dispatchMessage(Handler.java:99)
06-13 11:20:39.728 13780 13876 E GoogleApiManager: 	at chad.mH(:com.google.android.gms@252234029@25.22.34 (190400-769260661):1)
06-13 11:20:39.728 13780 13876 E GoogleApiManager: 	at chad.dispatchMessage(:com.google.android.gms@252234029@25.22.34 (190400-769260661):5)
06-13 11:20:39.728 13780 13876 E GoogleApiManager: 	at android.os.Looper.loopOnce(Looper.java:211)
06-13 11:20:39.728 13780 13876 E GoogleApiManager: 	at android.os.Looper.loop(Looper.java:300)
06-13 11:20:39.728 13780 13876 E GoogleApiManager: 	at android.os.HandlerThread.run(HandlerThread.java:67)
06-13 11:20:39.729  2032  6212 E AppOps  : Bad call made by uid 10189. Package "com.google.android.gms" does not belong to uid 10209.
06-13 11:20:39.730 13780 13876 E GoogleApiManager: Failed to get service from broker. 
06-13 11:20:39.730 13780 13876 E GoogleApiManager: java.lang.SecurityException: Unknown calling package name 'com.google.android.gms'.
06-13 11:20:39.730 13780 13876 E GoogleApiManager: 	at android.os.Parcel.createExceptionOrNull(Parcel.java:3011)
06-13 11:20:39.730 13780 13876 E GoogleApiManager: 	at android.os.Parcel.createException(Parcel.java:2995)
06-13 11:20:39.730 13780 13876 E GoogleApiManager: 	at android.os.Parcel.readException(Parcel.java:2978)
06-13 11:20:39.730 13780 13876 E GoogleApiManager: 	at android.os.Parcel.readException(Parcel.java:2920)
06-13 11:20:39.730 13780 13876 E GoogleApiManager: 	at axtl.a(:com.google.android.gms@252234029@25.22.34 (190400-769260661):36)
06-13 11:20:39.730 13780 13876 E GoogleApiManager: 	at axrs.z(:com.google.android.gms@252234029@25.22.34 (190400-769260661):143)
06-13 11:20:39.730 13780 13876 E GoogleApiManager: 	at awyv.run(:com.google.android.gms@252234029@25.22.34 (190400-769260661):42)
06-13 11:20:39.730 13780 13876 E GoogleApiManager: 	at android.os.Handler.handleCallback(Handler.java:942)
06-13 11:20:39.730 13780 13876 E GoogleApiManager: 	at android.os.Handler.dispatchMessage(Handler.java:99)
06-13 11:20:39.730 13780 13876 E GoogleApiManager: 	at chad.mH(:com.google.android.gms@252234029@25.22.34 (190400-769260661):1)
06-13 11:20:39.730 13780 13876 E GoogleApiManager: 	at chad.dispatchMessage(:com.google.android.gms@252234029@25.22.34 (190400-769260661):5)
06-13 11:20:39.730 13780 13876 E GoogleApiManager: 	at android.os.Looper.loopOnce(Looper.java:211)
06-13 11:20:39.730 13780 13876 E GoogleApiManager: 	at android.os.Looper.loop(Looper.java:300)
06-13 11:20:39.730 13780 13876 E GoogleApiManager: 	at android.os.HandlerThread.run(HandlerThread.java:67)
06-13 11:20:39.751 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog Request 0xb400007c3c8bffc0: 0 https://drivefrontend-pa.googleapis.com/v1/items:get transport: drive::ds::Status::UNAVAILABLE_WHILE_OFFLINE reader: drive::ds::Status::SUCCESS
06-13 11:20:39.751 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog req.hdr 0xb400007c3c8bffc0: Content-Type=application/x-protobuf
06-13 11:20:39.751 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog req.hdr 0xb400007c3c8bffc0: X-Goog-FieldMask=responses(status,item(open_with_links,folder_color,trashed,has_visitor_permissions,shared_with_me_date_millis,has_own_permissions,folder_color_rgb,workspace_id,passively_subscribed,etag,version,detectors,applied_labels,client_encryption_details(decryption_metadata.aes_256_gcm_chunk_size,decryption_metadata.kacls_id,decryption_metadata.key_format,decryption_metadata.wrapped_key,decryption_metadata.jwt,decryption_metadata.kacls_name,decryption_metadata.guest_idp),title,mime_type,export_links,create_date_millis,has_thumbnail,owned_by_me,single_parent_id,team_drive_id,md5_checksum,recency_date_millis,contains_unsubscribed_children,id,explicitly_trashed,modified_by_me_date_millis,abuse_is_appealable,sharing_user(email_from_account,id,focus_user_id),action_item,readers_can_see_comments,recency_date_reason,approval_summaries,folder_features,published,last_modifying_user(id,focus_user_id,email_from_account),app
06-13 11:20:39.751 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog req.hdr 0xb400007c3c8bffc0: x-goog-ext-*********-bin=CgsI/gpCAEgBYgIIAw==
06-13 11:20:39.751 12841 12963 E Cello   : [12963:NonCelloThread] drive_v2_cloud_store.cc:7621:DumpToLog Response body 0xb400007c3c8bffc0: (no response body)
06-13 11:20:39.752 12841 12963 E Cello   : [12963:NonCelloThread] item_query_handler.cc:1178:OnCloudByIdQuery Failed to get items from the cloud: MessageLite at 0xb400007c19e4f620
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: [1] failed: com.google.android.apps.docs.common.sync.task.c: Failed to get sync item metadata..
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: com.google.android.apps.docs.common.sync.task.c: Failed to get sync item metadata.
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at com.google.android.apps.docs.common.sync.content.n$a.run(PG:341)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at java.lang.Thread.run(Thread.java:1012)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: Caused by: com.google.android.libraries.drive.core.j: Cello error 2. Network error Cleared request. Failed task: getFiles_257196860(FindByIdsRequest={"id": ["1Y6EKyhjiQPiC85n1suTv4dIUU44M120GzxfyvgNVIj0"],"requestDescriptor": {"reason": 1406},"skipLocalstore": true},fetchedCloud=true)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.h.a(PG:19)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.m.d(PG:71)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.item.au.h(PG:87)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.item.ap.a(PG:23)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.impl.cello.jni.SlimJni__Cello_ItemQueryCallback.call(PG:11)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.impl.cello.jni.SlimJni__HttpRequestContext.native_onError(Native Method)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.impl.cello.jni.SlimJni__HttpRequestContext.onError(PG:10)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at androidx.activity.h.run(PG:467)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.task.common.a.c(PG:3)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at com.google.android.apps.docs.editors.ritz.view.shared.q.call(PG:451)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at com.google.common.util.concurrent.be$b.a(PG:3)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at com.google.common.util.concurrent.ap.run(PG:19)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at com.google.common.util.concurrent.be.run(PG:5)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at java.lang.Thread.run(Thread.java:1012)
06-13 11:20:39.757 12841 13303 E ContentSyncWorker: 	at com.google.android.libraries.drive.core.executor.a.run(PG:6)
06-13 11:20:40.578  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
